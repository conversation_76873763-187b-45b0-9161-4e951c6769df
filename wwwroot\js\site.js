﻿// ===== THEME MANAGEMENT =====
class ThemeManager {
    constructor() {
        this.theme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme();
        this.setupToggle();
        this.updateIcon();
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        
        // Update meta theme-color for mobile browsers
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            metaThemeColor.setAttribute('content', 
                this.theme === 'dark' ? '#1e293b' : '#ffffff'
            );
        }
    }

    toggle() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        localStorage.setItem('theme', this.theme);
        this.applyTheme();
        this.updateIcon();
        this.animateToggle();
    }

    updateIcon() {
        const icon = document.querySelector('.theme-icon');
        if (icon) {
            icon.className = `fas ${this.theme === 'light' ? 'fa-moon' : 'fa-sun'} theme-icon`;
        }
    }

    animateToggle() {
        const icon = document.querySelector('.theme-icon');
        if (icon) {
            icon.style.transform = 'scale(0.8) rotate(180deg)';
            setTimeout(() => {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }, 150);
        }
    }

    setupToggle() {
        const toggleBtn = document.getElementById('themeToggle');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.toggle());
        }
    }

    getTheme() {
        return this.theme;
    }

    setTheme(theme) {
        if (['light', 'dark'].includes(theme)) {
            this.theme = theme;
            localStorage.setItem('theme', this.theme);
            this.applyTheme();
            this.updateIcon();
        }
    }
}
// ===== SIDEBAR MANAGEMENT =====
class SidebarManager {
    constructor() {
        this.sidebar = document.getElementById('sidebar');
        this.content = document.getElementById('content');
        this.toggleBtn = document.getElementById('sidebarToggle');
        this.isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        this.init();
    }

    init() {
        this.applyState();
        this.setupToggle();
        this.setupMobileToggle();
        this.setupResponsive();
    }

    applyState() {
        if (this.isCollapsed && this.sidebar && this.content) {
            this.sidebar.classList.add('collapsed');
            this.content.classList.add('expanded');
        }
    }

    toggle() {
        if (!this.sidebar || !this.content) return;

        this.isCollapsed = !this.isCollapsed;
        localStorage.setItem('sidebarCollapsed', this.isCollapsed);

        this.sidebar.classList.toggle('collapsed', this.isCollapsed);
        this.content.classList.toggle('expanded', this.isCollapsed);

        // Animate toggle button
        if (this.toggleBtn) {
            this.toggleBtn.style.transform = 'rotate(180deg)';
            setTimeout(() => {
                this.toggleBtn.style.transform = 'rotate(0deg)';
            }, 300);
        }
    }

    setupToggle() {
        if (this.toggleBtn) {
            this.toggleBtn.addEventListener('click', () => this.toggle());
        }
    }

    setupMobileToggle() {
        const mobileToggle = document.querySelector('[data-bs-target="#sidebar"]');
        if (mobileToggle && this.sidebar) {
            mobileToggle.addEventListener('click', () => {
                this.sidebar.classList.toggle('show');
            });
        }
    }

    setupResponsive() {
        window.addEventListener('resize', () => {
            if (window.innerWidth <= 991.98) {
                if (this.sidebar) {
                    this.sidebar.classList.remove('collapsed');
                }
                if (this.content) {
                    this.content.classList.remove('expanded');
                }
            } else {
                this.applyState();
            }
        });
    }
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    // Initialize managers
    window.themeManager = new ThemeManager();
    window.sidebarManager = new SidebarManager();

    // Initialize tooltips and popovers
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add active class to current navigation item
    const currentLocation = location.pathname;
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentLocation) {
            link.classList.add('active');
        }
    });

    // Handle card hover effects
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Form validation enhancement
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
});

// ===== UTILITY FUNCTIONS =====
const Utils = {
    // Format numbers with commas
    formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },

    // Debounce function calls
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Show notification
    showNotification(message, type = 'info', duration = 5000) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // Auto-dismiss
        if (duration > 0) {
            setTimeout(() => {
                alertDiv.remove();
            }, duration);
        }
        
        return alertDiv;
    },

    // Validate email
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
};

// ===== GLOBAL FUNCTIONS =====
window.showNotification = function(message, type = 'info', duration = 5000) {
    return Utils.showNotification(message, type, duration);
};

window.toggleTheme = function() {
    if (window.themeManager) {
        window.themeManager.toggle();
    }
};

window.setTheme = function(theme) {
    if (window.themeManager) {
        window.themeManager.setTheme(theme);
    }
};
