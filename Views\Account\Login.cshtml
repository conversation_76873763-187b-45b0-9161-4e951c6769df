@model RafoEvaluation.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "Login";
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - RafoEvaluation</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
</head>
<body class="login-page">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-form fade-in">
                    <div class="text-center mb-4">
                        <i class="fas fa-chart-line text-primary-custom" style="font-size: 3rem;"></i>
                        <h2 class="text-primary-custom mt-3 mb-2">RAFO Evaluation</h2>
                        <p class="text-muted">Sign in to your account</p>
                    </div>
                    
                    @if (!string.IsNullOrEmpty(ViewBag.Error as string))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @ViewBag.Error
                        </div>
                    }
                    
                    <form asp-action="Login" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="mb-3">
                            <label asp-for="ServiceNumber" class="form-label">Service Number</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-id-badge text-muted"></i>
                                </span>
                                <input asp-for="ServiceNumber" class="form-control" placeholder="Enter your service number" />
                            </div>
                            <span asp-validation-for="ServiceNumber" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock text-muted"></i>
                                </span>
                                <input asp-for="Password" type="password" class="form-control" placeholder="Enter your password" />
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                Remember me
                            </label>
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary-custom">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Sign In
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <a href="#" class="text-decoration-none">Forgot your password?</a>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <small class="text-muted">
                            Don't have an account? 
                            <a href="#" class="text-decoration-none fw-bold">Contact Administrator</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.querySelector('input[name="Password"]');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    </script>
</body>
</html>
