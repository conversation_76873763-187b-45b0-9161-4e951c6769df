@model RafoEvaluation.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = null;
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام تقييم القوات الجوية الملكية العمانية</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
    <!-- Google Fonts for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
</head>
<body class="login-page" dir="rtl">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-form fade-in">
                    <div class="text-center mb-4">
                        <i class="fas fa-chart-line text-primary-custom" style="font-size: 3rem;"></i>
                        <h2 class="text-primary-custom mt-3 mb-2">نظام تقييم القوات الجوية</h2>
                        <p class="text-muted">تسجيل الدخول إلى حسابك</p>
                    </div>

                    @if (!string.IsNullOrEmpty(ViewBag.Error as string))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle ms-2"></i>
                            @ViewBag.Error
                        </div>
                    }
                    
                    <form asp-action="Login" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="mb-3">
                            <label asp-for="ServiceNumber" class="form-label">الرقم العسكري</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-id-badge text-muted"></i>
                                </span>
                                <input asp-for="ServiceNumber" class="form-control" placeholder="أدخل رقمك العسكري" />
                            </div>
                            <span asp-validation-for="ServiceNumber" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock text-muted"></i>
                                </span>
                                <input asp-for="Password" type="password" class="form-control" placeholder="أدخل كلمة المرور" />
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="mb-3 form-check">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                تذكرني
                            </label>
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary-custom">
                                <i class="fas fa-sign-in-alt ms-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>

                        <div class="text-center">
                            <a href="#" class="text-decoration-none">نسيت كلمة المرور؟</a>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <small class="text-muted">
                            ليس لديك حساب؟
                            <a href="#" class="text-decoration-none fw-bold">تواصل مع المدير</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.querySelector('input[name="Password"]');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    </script>
</body>
</html>
