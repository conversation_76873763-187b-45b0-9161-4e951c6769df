﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class FixedSeedData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(5182));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6269));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6272));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6273));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6274));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6274));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6275));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6276));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6277));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6278));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6278));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6279));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6280));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6281));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6282));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6283));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(1369));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(2225));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(2227));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(2228));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(2229));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(2230));

            migrationBuilder.UpdateData(
                table: "UserRoles",
                keyColumn: "UserRoleId",
                keyValue: 1,
                column: "AssignedAt",
                value: new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "UserId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "Password" },
                values: new object[] { new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(1009));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(1968));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(1971));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(1974));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2004));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2005));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2006));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2007));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2008));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2009));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2009));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2010));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2011));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2012));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2013));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2014));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(7896));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(8618));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(8621));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(8622));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(8622));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(8623));

            migrationBuilder.UpdateData(
                table: "UserRoles",
                keyColumn: "UserRoleId",
                keyValue: 1,
                column: "AssignedAt",
                value: new DateTime(2025, 7, 8, 16, 0, 19, 26, DateTimeKind.Utc).AddTicks(3168));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "UserId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "Password" },
                values: new object[] { new DateTime(2025, 7, 8, 16, 0, 19, 26, DateTimeKind.Utc).AddTicks(1593), "$2a$11$HS9uUmY0ubh2MswZvxqwS.y/cH3d7RlorY1RLpeA4U/8Tx/taiiEW" });
        }
    }
}
