using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using RafoEvaluation.Models.Auth;

namespace RafoEvaluation.Models
{
    public class Candidate
    {
        [Key]
        public int CandidateId { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "Full Name")]
        public string FullName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Category")]
        public int CategoryId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Service Number")]
        public string ServiceNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Display(Name = "National ID Number")]
        public string NationalIdNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Rank")]
        public int RankId { get; set; }

        [Required]
        [Display(Name = "Airbase ID")]
        public int AirbaseId { get; set; }

        [StringLength(200)]
        [Display(Name = "Department")]
        public string? Department { get; set; }

        [StringLength(200)]
        [Display(Name = "Major")]
        public string? Major { get; set; }

        [StringLength(200)]
        [Display(Name = "University")]
        public string? University { get; set; }

        [Display(Name = "Graduation Year")]
        public int? GraduationYear { get; set; }

        [StringLength(10)]
        [Display(Name = "Marks Grade")]
        public string? MarksGrade { get; set; }

        [Required]
        [Display(Name = "Date of Birth")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [StringLength(200)]
        [Display(Name = "Job Title")]
        public string? JobTitle { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Created At")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "Updated At")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties
        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; } = null!;

        [ForeignKey("RankId")]
        public virtual Rank Rank { get; set; } = null!;

        [ForeignKey("AirbaseId")]
        public virtual Airbase Airbase { get; set; } = null!;

        // Calculated Properties
        [NotMapped]
        public int Age => DateTime.Now.Year - DateOfBirth.Year - (DateTime.Now.DayOfYear < DateOfBirth.DayOfYear ? 1 : 0);
    }
}
