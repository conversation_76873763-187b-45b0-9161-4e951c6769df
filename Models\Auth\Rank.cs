using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.Models.Auth
{
    public class Rank
    {
        [Key]
        public int RankId { get; set; }

        [Required]
        [StringLength(100)]
        public string RankName { get; set; } = string.Empty;

        [StringLength(255)]
        public string? Description { get; set; }

        public int DisplayOrder { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ICollection<User> Users { get; set; } = new List<User>();
    }
}
