@model RafoEvaluation.ViewModels.CandidateViewModel
@{
    ViewData["Title"] = "Candidate Details";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>Candidate Details
                    </h3>
                    <div>
                        <a asp-action="Edit" asp-route-id="@Model.CandidateId" class="btn btn-warning">
                            <i class="fas fa-edit me-1"></i>Edit
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-list me-1"></i>Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>Personal Information
                            </h5>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">Full Name:</label>
                                <p class="form-control-plaintext">@Model.FullName</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Service Number:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-secondary">@Model.ServiceNumber</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">National ID Number:</label>
                                <p class="form-control-plaintext">@Model.NationalIdNumber</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Date of Birth:</label>
                                <p class="form-control-plaintext">
                                    @Model.DateOfBirth.ToString("dd MMMM yyyy")
                                    <span class="text-muted">(@Model.Age years old)</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Job Title:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.JobTitle))
                                    {
                                        @Model.JobTitle
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">Not specified</span>
                                    }
                                </p>
                            </div>
                        </div>

                        <!-- Official Information -->
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-id-badge me-2"></i>Official Information
                            </h5>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Category:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-info">@Model.CategoryName</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Rank:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-warning text-dark">@Model.RankName</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Airbase ID:</label>
                                <p class="form-control-plaintext">@Model.AirbaseId</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Department:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.Department))
                                    {
                                        @Model.Department
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">Not specified</span>
                                    }
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Status:</label>
                                <p class="form-control-plaintext">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">Inactive</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Education Information -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-graduation-cap me-2"></i>Education Information
                            </h5>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">University:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.University))
                                    {
                                        @Model.University
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">Not specified</span>
                                    }
                                </p>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Major:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.Major))
                                    {
                                        @Model.Major
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">Not specified</span>
                                    }
                                </p>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Graduation Year:</label>
                                <p class="form-control-plaintext">
                                    @if (Model.GraduationYear.HasValue)
                                    {
                                        @Model.GraduationYear.Value
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">Not specified</span>
                                    }
                                </p>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Marks Grade:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.MarksGrade))
                                    {
                                        <span class="badge bg-success">@Model.MarksGrade</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">Not specified</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>System Information
                            </h5>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Created At:</label>
                                <p class="form-control-plaintext">
                                    @Model.CreatedAt.ToString("dd MMMM yyyy, HH:mm")
                                </p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Updated At:</label>
                                <p class="form-control-plaintext">
                                    @if (Model.UpdatedAt.HasValue)
                                    {
                                        @Model.UpdatedAt.Value.ToString("dd MMMM yyyy, HH:mm")
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">Never updated</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
