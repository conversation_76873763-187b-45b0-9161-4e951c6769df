@model RafoEvaluation.ViewModels.CategoryCreateViewModel
@{
    ViewData["Title"] = "إضافة فئة جديدة";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="fas fa-plus breadcrumb-icon"></i>
                    إضافة فئة جديدة
                </h1>
                <p class="page-subtitle">إنشاء فئة جديدة للمرشحين والتخصصات</p>
            </div>
            <div class="col-md-4">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Category">
                                <i class="fas fa-list-alt"></i> الفئات
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-plus"></i> إضافة جديد
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-plus ms-2"></i>إضافة فئة جديدة
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post" id="createCategoryForm">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="CategoryCode" class="form-label">
                                    <i class="fas fa-code ms-1"></i>رمز الفئة <span class="text-danger">*</span>
                                </label>
                                <input asp-for="CategoryCode" class="form-control" placeholder="مثال: CAT001" />
                                <span asp-validation-for="CategoryCode" class="text-danger"></span>
                                <div class="form-text">رمز فريد للفئة (حروف وأرقام فقط)</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="CategoryName" class="form-label">
                                    <i class="fas fa-tag ms-1"></i>اسم الفئة <span class="text-danger">*</span>
                                </label>
                                <input asp-for="CategoryName" class="form-control" placeholder="أدخل اسم الفئة" />
                                <span asp-validation-for="CategoryName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">
                                <i class="fas fa-align-right ms-1"></i>الوصف
                            </label>
                            <textarea asp-for="Description" class="form-control" rows="4"
                                      placeholder="وصف مفصل للفئة (اختياري)"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" checked />
                                    <label asp-for="IsActive" class="form-check-label">
                                        <i class="fas fa-toggle-on ms-1"></i>فئة نشطة
                                    </label>
                                    <div class="form-text">الفئات النشطة فقط تظهر في قوائم الاختيار</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to List
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>Create Category
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
