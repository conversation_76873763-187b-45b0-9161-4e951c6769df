﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using RafoEvaluation.Data;

#nullable disable

namespace RafoEvaluation.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250709040114_NewMigration")]
    partial class NewMigration
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.6");

            modelBuilder.Entity("RafoEvaluation.Models.Airbase", b =>
                {
                    b.Property<int>("AirbaseId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AirbaseName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.<PERSON>("AirbaseId");

                    b.To<PERSON>("Airbases");

                    b.<PERSON>(
                        new
                        {
                            AirbaseId = 1,
                            AirbaseName = "قيادة سلاح الجو السلطاني العماني"
                        },
                        new
                        {
                            AirbaseId = 2,
                            AirbaseName = "قاعدة غلا وأكاديمية السلطان قابوس الجوية"
                        },
                        new
                        {
                            AirbaseId = 3,
                            AirbaseName = "قاعدة السيب الجوية"
                        },
                        new
                        {
                            AirbaseId = 4,
                            AirbaseName = "قاعدة صلالة الجوية"
                        },
                        new
                        {
                            AirbaseId = 5,
                            AirbaseName = "قاعدة المصنعة الجوية"
                        },
                        new
                        {
                            AirbaseId = 6,
                            AirbaseName = "قاعدة مصيرة الجوية"
                        },
                        new
                        {
                            AirbaseId = 7,
                            AirbaseName = "قاعدة أدم الجوية"
                        },
                        new
                        {
                            AirbaseId = 8,
                            AirbaseName = "قاعدة ثمريت الجوية"
                        },
                        new
                        {
                            AirbaseId = 9,
                            AirbaseName = "قاعدة خصب الجوية"
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.Rank", b =>
                {
                    b.Property<int>("RankId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("INTEGER");

                    b.Property<string>("RankName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("RankId");

                    b.HasIndex("RankName")
                        .IsUnique()
                        .HasDatabaseName("IX_Ranks_RankName");

                    b.ToTable("Ranks");

                    b.HasData(
                        new
                        {
                            RankId = 1,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(2551),
                            Description = "Senior Air Officer",
                            DisplayOrder = 1,
                            RankName = "Air Marshal"
                        },
                        new
                        {
                            RankId = 2,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3225),
                            Description = "Senior Air Officer",
                            DisplayOrder = 2,
                            RankName = "Air Vice Marshal"
                        },
                        new
                        {
                            RankId = 3,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3227),
                            Description = "Senior Air Officer",
                            DisplayOrder = 3,
                            RankName = "Air Commodore"
                        },
                        new
                        {
                            RankId = 4,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3228),
                            Description = "Senior Officer",
                            DisplayOrder = 4,
                            RankName = "Group Captain"
                        },
                        new
                        {
                            RankId = 5,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3239),
                            Description = "Senior Officer",
                            DisplayOrder = 5,
                            RankName = "Wing Commander"
                        },
                        new
                        {
                            RankId = 6,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3240),
                            Description = "Officer",
                            DisplayOrder = 6,
                            RankName = "Squadron Leader"
                        },
                        new
                        {
                            RankId = 7,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3241),
                            Description = "Officer",
                            DisplayOrder = 7,
                            RankName = "Flight Lieutenant"
                        },
                        new
                        {
                            RankId = 8,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3241),
                            Description = "Junior Officer",
                            DisplayOrder = 8,
                            RankName = "Flying Officer"
                        },
                        new
                        {
                            RankId = 9,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3242),
                            Description = "Junior Officer",
                            DisplayOrder = 9,
                            RankName = "Pilot Officer"
                        },
                        new
                        {
                            RankId = 10,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3243),
                            Description = "Senior NCO",
                            DisplayOrder = 10,
                            RankName = "Warrant Officer"
                        },
                        new
                        {
                            RankId = 11,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3244),
                            Description = "NCO",
                            DisplayOrder = 11,
                            RankName = "Flight Sergeant"
                        },
                        new
                        {
                            RankId = 12,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3245),
                            Description = "NCO",
                            DisplayOrder = 12,
                            RankName = "Sergeant"
                        },
                        new
                        {
                            RankId = 13,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3246),
                            Description = "Junior NCO",
                            DisplayOrder = 13,
                            RankName = "Corporal"
                        },
                        new
                        {
                            RankId = 14,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3246),
                            Description = "Enlisted",
                            DisplayOrder = 14,
                            RankName = "Senior Aircraftman"
                        },
                        new
                        {
                            RankId = 15,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3247),
                            Description = "Enlisted",
                            DisplayOrder = 15,
                            RankName = "Leading Aircraftman"
                        },
                        new
                        {
                            RankId = 16,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3248),
                            Description = "Enlisted",
                            DisplayOrder = 16,
                            RankName = "Aircraftman"
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.Role", b =>
                {
                    b.Property<int>("RoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("RoleId");

                    b.HasIndex("RoleName")
                        .IsUnique()
                        .HasDatabaseName("IX_Roles_RoleName");

                    b.ToTable("Roles");

                    b.HasData(
                        new
                        {
                            RoleId = 1,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7443),
                            Description = "System Administrator with full access",
                            RoleName = "Admin"
                        },
                        new
                        {
                            RoleId = 2,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7942),
                            Description = "Evaluation Coordinator",
                            RoleName = "Coordinator"
                        },
                        new
                        {
                            RoleId = 3,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7943),
                            Description = "Personnel Evaluator",
                            RoleName = "Evaluator"
                        },
                        new
                        {
                            RoleId = 4,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7944),
                            Description = "Read-only access to evaluations",
                            RoleName = "Viewer"
                        },
                        new
                        {
                            RoleId = 5,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7945),
                            Description = "Human Resources Manager",
                            RoleName = "HR Manager"
                        },
                        new
                        {
                            RoleId = 6,
                            CreatedAt = new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7946),
                            Description = "Training and Development Officer",
                            RoleName = "Training Officer"
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<int>("RankId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ServiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("UserId");

                    b.HasIndex("RankId");

                    b.HasIndex("ServiceNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_ServiceNumber");

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            UserId = 1,
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            IsActive = true,
                            Password = "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
                            RankId = 3,
                            ServiceNumber = "RAFO001"
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.UserRole", b =>
                {
                    b.Property<int>("UserRoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("AssignedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("UserRoleId");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId", "RoleId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserRoles_UserId_RoleId");

                    b.ToTable("UserRoles");

                    b.HasData(
                        new
                        {
                            UserRoleId = 1,
                            AssignedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            RoleId = 1,
                            UserId = 1
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Candidate", b =>
                {
                    b.Property<int>("CandidateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AirbaseId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CategoryId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("TEXT");

                    b.Property<string>("Department")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int?>("GraduationYear")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<string>("JobTitle")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Major")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("MarksGrade")
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<string>("NationalIdNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("RankId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ServiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("University")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("CandidateId");

                    b.HasIndex("AirbaseId");

                    b.HasIndex("CategoryId");

                    b.HasIndex("FullName")
                        .HasDatabaseName("IX_Candidates_FullName");

                    b.HasIndex("NationalIdNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Candidates_NationalIdNumber");

                    b.HasIndex("RankId");

                    b.HasIndex("ServiceNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Candidates_ServiceNumber");

                    b.ToTable("Candidates");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Category", b =>
                {
                    b.Property<int>("CategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CategoryCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<string>("CategoryName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("CategoryId");

                    b.ToTable("Categories");

                    b.HasData(
                        new
                        {
                            CategoryId = 1,
                            CategoryCode = "CAT-PLT",
                            CategoryName = "المرشحيين الطيارين",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "فئة المرشحين للالتحاق بسلاح الجو السلطاني العماني كمرشحين طيارين.",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 2,
                            CategoryCode = "CAT-MUG",
                            CategoryName = "المرشحيين الجامعيين العسكريين",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط عسكريين جامعيين",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 3,
                            CategoryCode = "CAT-CUG",
                            CategoryName = "المرشحيين الجامعيين المدنيين",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط مدنيين جامعيين",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 4,
                            CategoryCode = "CAT-LSO",
                            CategoryName = "ضباط الخدمة المحدودة",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "ضباط الصف من ذوي الخدمة المحدودة وسسبق لهم العمل في السلاح وهم برتبة وكيل فأعلى",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 5,
                            CategoryCode = "CAT-NCO",
                            CategoryName = "ضباط الصف ( رقباء / عرفاء)",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "ضباط صف برتبة رقيب أو عريف سبق لهم العمل بالسلاح",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 6,
                            CategoryCode = "CAT-TCN",
                            CategoryName = "ضباط الصف الكلية التقنية العسكرية",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "ضباط صف خريجوا الكلية العسكرية التقنية",
                            IsActive = true
                        },
                        new
                        {
                            CategoryId = 7,
                            CategoryCode = "CAT-CNP",
                            CategoryName = "ضباط الصف المدنيين للترفيع",
                            CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "ضباط صف مدنيين مرشحين للترقية بالصفة المدنية",
                            IsActive = true
                        });
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.User", b =>
                {
                    b.HasOne("RafoEvaluation.Models.Auth.Rank", "Rank")
                        .WithMany("Users")
                        .HasForeignKey("RankId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Rank");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.UserRole", b =>
                {
                    b.HasOne("RafoEvaluation.Models.Auth.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("RafoEvaluation.Models.Auth.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Candidate", b =>
                {
                    b.HasOne("RafoEvaluation.Models.Airbase", "Airbase")
                        .WithMany("Candidates")
                        .HasForeignKey("AirbaseId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("RafoEvaluation.Models.Category", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("RafoEvaluation.Models.Auth.Rank", "Rank")
                        .WithMany()
                        .HasForeignKey("RankId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Airbase");

                    b.Navigation("Category");

                    b.Navigation("Rank");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Airbase", b =>
                {
                    b.Navigation("Candidates");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.Rank", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.Role", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("RafoEvaluation.Models.Auth.User", b =>
                {
                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
