@model RafoEvaluation.ViewModels.AirbaseListViewModel
@{
    ViewData["Title"] = "القواعد الجوية";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-building ms-2"></i>إدارة القواعد الجوية
                    </h3>
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus ms-1"></i>إضافة قاعدة جوية جديدة
                    </a>
                </div>
                <div class="card-body">
                    <!-- نموذج البحث -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" name="searchTerm" value="@Model.SearchTerm"
                                           class="form-control" placeholder="البحث باسم القاعدة الجوية...">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                @if (!string.IsNullOrEmpty(Model.SearchTerm))
                                {
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-times ms-1"></i>مسح
                                    </a>
                                }
                            </div>
                        </div>
                    </form>

                    <!-- Results Info -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="text-muted mb-0">
                                Showing @((Model.CurrentPage - 1) * Model.PageSize + 1) to 
                                @(Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCount)) of 
                                @Model.TotalCount entries
                                @if (!string.IsNullOrEmpty(Model.SearchTerm))
                                {
                                    <span>(filtered from total entries)</span>
                                }
                            </p>
                        </div>
                    </div>

                    <!-- جدول القواعد الجوية -->
                    @if (Model.Airbases.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                       
                                        <th> القاعدة الجوية </th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.Airbases)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">@item.AirbaseId</span>
                                            </td>
                                            <td>
                                               @item.AirbaseName
                                            </td>
                                          
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.AirbaseId" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.AirbaseId" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.AirbaseId" 
                                                       class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (Model.TotalPages > 1)
                        {
                            <nav aria-label="Airbases pagination">
                                <ul class="pagination justify-content-center">
                                    <!-- Previous -->
                                    <li class="page-item @(Model.CurrentPage == 1 ? "disabled" : "")">
                                        <a class="page-link" asp-action="Index" 
                                           asp-route-page="@(Model.CurrentPage - 1)" 
                                           asp-route-searchTerm="@Model.SearchTerm">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    </li>

                                    <!-- Page Numbers -->
                                    @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                            <a class="page-link" asp-action="Index" 
                                               asp-route-page="@i" 
                                               asp-route-searchTerm="@Model.SearchTerm">
                                                @i
                                            </a>
                                        </li>
                                    }

                                    <!-- Next -->
                                    <li class="page-item @(Model.CurrentPage == Model.TotalPages ? "disabled" : "")">
                                        <a class="page-link" asp-action="Index" 
                                           asp-route-page="@(Model.CurrentPage + 1)" 
                                           asp-route-searchTerm="@Model.SearchTerm">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No airbases found</h4>
                            @if (!string.IsNullOrEmpty(Model.SearchTerm))
                            {
                                <p class="text-muted">
                                    No airbases match your search criteria. 
                                    <a asp-action="Index" class="text-decoration-none">Clear search</a> to see all airbases.
                                </p>
                            }
                            else
                            {
                                <p class="text-muted">
                                    Get started by <a asp-action="Create" class="text-decoration-none">adding your first airbase</a>.
                                </p>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
