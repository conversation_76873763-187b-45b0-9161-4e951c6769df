﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class AddCategoryEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Categories",
                columns: table => new
                {
                    CategoryId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CategoryName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    CategoryCode = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Categories", x => x.CategoryId);
                });

            migrationBuilder.InsertData(
                table: "Categories",
                columns: new[] { "CategoryId", "CategoryCode", "CategoryName", "CreatedAt", "Description", "IsActive", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, "CAT-PLT", "المرشحيين الطيارين", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "فئة المرشحين للالتحاق بسلاح الجو السلطاني العماني كمرشحين طيارين.", true, null },
                    { 2, "CAT-MUG", "المرشحيين الجامعيين العسكريين", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط عسكريين جامعيين", true, null },
                    { 3, "CAT-CUG", "المرشحيين الجامعيين المدنيين", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط مدنيين جامعيين", true, null },
                    { 4, "CAT-LSO", "ضباط الخدمة المحدودة", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط الصف من ذوي الخدمة المحدودة وسسبق لهم العمل في السلاح وهم برتبة وكيل فأعلى", true, null },
                    { 5, "CAT-NCO", "ضباط الصف ( رقباء / عرفاء)", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط صف برتبة رقيب أو عريف سبق لهم العمل بالسلاح", true, null },
                    { 6, "CAT-TCN", "ضباط الصف الكلية التقنية العسكرية", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط صف خريجوا الكلية العسكرية التقنية", true, null },
                    { 7, "CAT-CNP", "ضباط الصف المدنيين للترفيع", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط صف مدنيين مرشحين للترقية بالصفة المدنية", true, null }
                });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 433, DateTimeKind.Utc).AddTicks(9280));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(188));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(192));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(193));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(194));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(195));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(196));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(197));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(197));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(198));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(199));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(200));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(201));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(201));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(225));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(226));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(5688));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(6378));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(6380));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(6381));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(6382));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 19, 6, 434, DateTimeKind.Utc).AddTicks(6382));

            migrationBuilder.CreateIndex(
                name: "IX_Categories_CategoryCode",
                table: "Categories",
                column: "CategoryCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Categories_CategoryName",
                table: "Categories",
                column: "CategoryName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Categories");

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(5182));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6269));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6272));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6273));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6274));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6274));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6275));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6276));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6277));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6278));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6278));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6279));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6280));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6281));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6282));

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 556, DateTimeKind.Utc).AddTicks(6283));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 1,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(1369));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 2,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(2225));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 3,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(2227));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 4,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(2228));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 5,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(2229));

            migrationBuilder.UpdateData(
                table: "Roles",
                keyColumn: "RoleId",
                keyValue: 6,
                column: "CreatedAt",
                value: new DateTime(2025, 7, 8, 16, 5, 34, 557, DateTimeKind.Utc).AddTicks(2230));
        }
    }
}
