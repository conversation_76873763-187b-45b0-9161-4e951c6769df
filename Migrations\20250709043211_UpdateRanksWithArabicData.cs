﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class UpdateRanksWithArabicData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Update existing ranks 1-16 with Arabic names and correct ordering
            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 1,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "جندي", 17, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 2,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "نائب عريف", 16, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 3,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "عريف", 15, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 4,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "رقيب", 14, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 5,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "رقيب أول", 13, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 6,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "وكيل", 12, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 7,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "وكيل أول", 11, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 8,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "ضابط مرشح", 10, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 9,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "ملازم ثاني", 9, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 10,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "ملازم أول", 8, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 11,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "نقيب", 7, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 12,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "رائد", 6, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 13,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "مقدم", 5, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 14,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "عقيد", 4, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 15,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "عميد", 3, "Military Rank" });

            migrationBuilder.UpdateData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValue: 16,
                columns: new[] { "RankName", "DisplayOrder", "Description" },
                values: new object[] { "لواء", 2, "Military Rank" });

            // Insert new ranks 17-34
            migrationBuilder.InsertData(
                table: "Ranks",
                columns: new[] { "RankId", "RankName", "DisplayOrder", "Description", "CreatedAt" },
                values: new object[,]
                {
                    { 17, "فريق", 1, "Military Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 18, "مدني درجة 16", 34, "Civilian Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 19, "مدني درجة 15", 33, "Civilian Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 20, "مدني درجة 14", 32, "Civilian Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 21, "مدني درجة 13", 31, "Civilian Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 22, "مدني درجة 12", 30, "Civilian Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 23, "مدني درجة 11", 29, "Civilian Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 24, "مدني درجة 10", 28, "Civilian Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 25, "مدني درجة 9", 27, "Civilian Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 26, "ضابط مدني د8", 26, "Civilian Officer Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 27, "ضابط مدني د9", 25, "Civilian Officer Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 28, "ضابط مدني د7", 24, "Civilian Officer Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 29, "ضابط مدني د6", 23, "Civilian Officer Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 30, "ضابط مدني د5", 22, "Civilian Officer Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 31, "ضابط مدني د4", 21, "Civilian Officer Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 32, "ضابط مدني د3", 20, "Civilian Officer Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 33, "ضابط مدني د2", 19, "Civilian Officer Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) },
                    { 34, "ضابط مدني د1", 18, "Civilian Officer Rank", new DateTime(2025, 7, 9, 4, 32, 10, 730, DateTimeKind.Utc) }
                });

            // Update the admin user to use the highest military rank (فريق - RankId 17) and correct password
            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "UserId",
                keyValue: 1,
                columns: new[] { "RankId", "Password" },
                values: new object[] { 17, "$2a$11$ef3G8GpupKKStOCT1sSdTOQ0wa.StUPaEYSt33Qge7I0kmbdNE5VS" });


        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Delete all Arabic ranks
            migrationBuilder.DeleteData(
                table: "Ranks",
                keyColumn: "RankId",
                keyValues: new object[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34 });

            // Restore original English ranks
            migrationBuilder.InsertData(
                table: "Ranks",
                columns: new[] { "RankId", "RankName", "DisplayOrder", "Description", "CreatedAt" },
                values: new object[,]
                {
                    { 1, "Air Marshal", 1, "Senior Air Officer", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(2551) },
                    { 2, "Air Vice Marshal", 2, "Senior Air Officer", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3225) },
                    { 3, "Air Commodore", 3, "Senior Air Officer", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3227) },
                    { 4, "Group Captain", 4, "Senior Officer", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3228) },
                    { 5, "Wing Commander", 5, "Senior Officer", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3239) },
                    { 6, "Squadron Leader", 6, "Officer", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3240) },
                    { 7, "Flight Lieutenant", 7, "Officer", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3241) },
                    { 8, "Flying Officer", 8, "Junior Officer", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3241) },
                    { 9, "Pilot Officer", 9, "Junior Officer", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3242) },
                    { 10, "Warrant Officer", 10, "Senior NCO", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3243) },
                    { 11, "Flight Sergeant", 11, "NCO", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3244) },
                    { 12, "Sergeant", 12, "NCO", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3245) },
                    { 13, "Corporal", 13, "Junior NCO", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3246) },
                    { 14, "Senior Aircraftman", 14, "Enlisted", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3246) },
                    { 15, "Leading Aircraftman", 15, "Enlisted", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3247) },
                    { 16, "Aircraftman", 16, "Enlisted", new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3248) }
                });

            // Restore admin user to original rank (Air Commodore = RankId 3)
            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "UserId",
                keyValue: 1,
                columns: new[] { "RankId", "Password" },
                values: new object[] { 3, "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi" });


        }
    }
}
