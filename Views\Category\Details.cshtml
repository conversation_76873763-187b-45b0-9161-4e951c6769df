@model RafoEvaluation.ViewModels.CategoryViewModel
@{
    ViewData["Title"] = "Category Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Category Details
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>Category Name:</strong>
                                </div>
                                <div class="col-sm-9">
                                    @Model.CategoryName
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>Category Code:</strong>
                                </div>
                                <div class="col-sm-9">
                                    <span class="badge bg-secondary fs-6">@Model.CategoryCode</span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>Description:</strong>
                                </div>
                                <div class="col-sm-9">
                                    @if (!string.IsNullOrEmpty(Model.Description))
                                    {
                                        <p class="mb-0">@Model.Description</p>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">No description provided</span>
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>Status:</strong>
                                </div>
                                <div class="col-sm-9">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>Inactive
                                        </span>
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>Created:</strong>
                                </div>
                                <div class="col-sm-9">
                                    @Model.CreatedAt.ToString("MMMM dd, yyyy 'at' h:mm tt")
                                </div>
                            </div>

                            @if (Model.UpdatedAt.HasValue)
                            {
                                <div class="row mb-3">
                                    <div class="col-sm-3">
                                        <strong>Last Updated:</strong>
                                    </div>
                                    <div class="col-sm-9">
                                        @Model.UpdatedAt.Value.ToString("MMMM dd, yyyy 'at' h:mm tt")
                                    </div>
                                </div>
                            }
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-tools me-1"></i>Actions
                                    </h6>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Edit" asp-route-id="@Model.CategoryId" 
                                           class="btn btn-warning">
                                            <i class="fas fa-edit me-1"></i>Edit Category
                                        </a>
                                        <button type="button" class="btn btn-primary" 
                                                onclick="toggleStatus(@Model.CategoryId, '@Model.CategoryName')">
                                            <i class="fas fa-toggle-@(Model.IsActive ? "on" : "off") me-1"></i>
                                            @(Model.IsActive ? "Deactivate" : "Activate")
                                        </button>
                                        <a asp-action="Delete" asp-route-id="@Model.CategoryId" 
                                           class="btn btn-danger">
                                            <i class="fas fa-trash me-1"></i>Delete Category
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-start mt-4">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Toggle Status -->
<script>
    function toggleStatus(categoryId, categoryName) {
        if (confirm(`Are you sure you want to toggle the status of "${categoryName}"?`)) {
            fetch(`/Category/ToggleStatus/${categoryId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'An error occurred while updating the status.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the status.');
            });
        }
    }
</script>
