using RafoEvaluation.Models.Auth;

namespace RafoEvaluation.Services
{
    public interface IAuthenticationService
    {
        Task<User?> AuthenticateAsync(string serviceNumber, string password);
        Task<User?> GetUserByIdAsync(int userId);
        Task<User?> GetUserByServiceNumberAsync(string serviceNumber);
        Task<bool> CreateUserAsync(User user, string password, List<int> roleIds);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<bool> ResetPasswordAsync(int userId, string newPassword);
        Task<bool> ActivateUserAsync(int userId);
        Task<bool> DeactivateUserAsync(int userId);
        Task<List<User>> GetAllUsersAsync();
        Task<List<User>> GetActiveUsersAsync();
        Task<bool> AssignRoleAsync(int userId, int roleId);
        Task<bool> RemoveRoleAsync(int userId, int roleId);
        string HashPassword(string password);
        bool VerifyPassword(string password, string hashedPassword);
    }
}
