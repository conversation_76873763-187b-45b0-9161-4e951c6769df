using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models;
using RafoEvaluation.ViewModels;

namespace RafoEvaluation.Controllers
{
    public class CandidateController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CandidateController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Candidate
        public async Task<IActionResult> Index(string searchTerm = "", int categoryFilter = 0, int rankFilter = 0, int page = 1)
        {
            const int pageSize = 10;

            var query = _context.Candidates
                .Include(c => c.Category)
                .Include(c => c.Rank)
                .AsQueryable();

            // Apply search filter
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(c => c.FullName.Contains(searchTerm) ||
                                        c.ServiceNumber.Contains(searchTerm) ||
                                        c.NationalIdNumber.Contains(searchTerm) ||
                                        (!string.IsNullOrEmpty(c.Department) && c.Department.Contains(searchTerm)) ||
                                        (!string.IsNullOrEmpty(c.JobTitle) && c.JobTitle.Contains(searchTerm)));
            }

            // Apply category filter
            if (categoryFilter > 0)
            {
                query = query.Where(c => c.CategoryId == categoryFilter);
            }

            // Apply rank filter
            if (rankFilter > 0)
            {
                query = query.Where(c => c.RankId == rankFilter);
            }

            // Get total count for pagination
            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

            // Apply pagination
            var candidates = await query
                .OrderBy(c => c.FullName)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(c => new CandidateViewModel
                {
                    CandidateId = c.CandidateId,
                    FullName = c.FullName,
                    ServiceNumber = c.ServiceNumber,
                    NationalIdNumber = c.NationalIdNumber,
                    CategoryId = c.CategoryId,
                    CategoryName = c.Category.CategoryName,
                    RankId = c.RankId,
                    RankName = c.Rank.RankName,
                    AirbaseId = c.AirbaseId,
                    Department = c.Department,
                    Major = c.Major,
                    University = c.University,
                    GraduationYear = c.GraduationYear,
                    MarksGrade = c.MarksGrade,
                    DateOfBirth = c.DateOfBirth,
                    JobTitle = c.JobTitle,
                    IsActive = c.IsActive,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt
                })
                .ToListAsync();

            // Get filter options
            var categories = await _context.Categories.Where(c => c.IsActive).ToListAsync();
            var ranks = await _context.Ranks.OrderBy(r => r.DisplayOrder).ToListAsync();

            var viewModel = new CandidateListViewModel
            {
                Candidates = candidates,
                SearchTerm = searchTerm,
                CategoryFilter = categoryFilter,
                RankFilter = rankFilter,
                CurrentPage = page,
                TotalPages = totalPages,
                TotalCount = totalCount,
                PageSize = pageSize,
                Categories = categories,
                Ranks = ranks
            };

            return View(viewModel);
        }

        // GET: Candidate/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var candidate = await _context.Candidates
                .Include(c => c.Category)
                .Include(c => c.Rank)
                .FirstOrDefaultAsync(c => c.CandidateId == id);

            if (candidate == null)
            {
                return NotFound();
            }

            var viewModel = new CandidateViewModel
            {
                CandidateId = candidate.CandidateId,
                FullName = candidate.FullName,
                ServiceNumber = candidate.ServiceNumber,
                NationalIdNumber = candidate.NationalIdNumber,
                CategoryId = candidate.CategoryId,
                CategoryName = candidate.Category.CategoryName,
                RankId = candidate.RankId,
                RankName = candidate.Rank.RankName,
                AirbaseId = candidate.AirbaseId,
                Department = candidate.Department,
                Major = candidate.Major,
                University = candidate.University,
                GraduationYear = candidate.GraduationYear,
                MarksGrade = candidate.MarksGrade,
                DateOfBirth = candidate.DateOfBirth,
                JobTitle = candidate.JobTitle,
                IsActive = candidate.IsActive,
                CreatedAt = candidate.CreatedAt,
                UpdatedAt = candidate.UpdatedAt
            };

            return View(viewModel);
        }

        // GET: Candidate/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = new CandidateCreateViewModel
            {
                Categories = await _context.Categories.Where(c => c.IsActive).ToListAsync(),
                Ranks = await _context.Ranks.OrderBy(r => r.DisplayOrder).ToListAsync(),
                Airbases = await _context.Airbases.OrderBy(a => a.AirbaseId).ToListAsync()
            };

            return View(viewModel);
        }

        // POST: Candidate/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CandidateCreateViewModel model)
        {
            if (ModelState.IsValid)
            {
                // Check for duplicate service number
                var existingByServiceNumber = await _context.Candidates
                    .FirstOrDefaultAsync(c => c.ServiceNumber == model.ServiceNumber);
                if (existingByServiceNumber != null)
                {
                    ModelState.AddModelError("ServiceNumber", "Service number already exists.");
                }

                // Check for duplicate national ID
                var existingByNationalId = await _context.Candidates
                    .FirstOrDefaultAsync(c => c.NationalIdNumber == model.NationalIdNumber);
                if (existingByNationalId != null)
                {
                    ModelState.AddModelError("NationalIdNumber", "National ID number already exists.");
                }

                if (ModelState.IsValid)
                {
                    var candidate = new Candidate
                    {
                        FullName = model.FullName,
                        CategoryId = model.CategoryId,
                        ServiceNumber = model.ServiceNumber,
                        NationalIdNumber = model.NationalIdNumber,
                        RankId = model.RankId,
                        AirbaseId = model.AirbaseId,
                        Department = model.Department,
                        Major = model.Major,
                        University = model.University,
                        GraduationYear = model.GraduationYear,
                        MarksGrade = model.MarksGrade,
                        DateOfBirth = model.DateOfBirth,
                        JobTitle = model.JobTitle,
                        IsActive = model.IsActive,
                        CreatedAt = DateTime.UtcNow
                    };

                    _context.Candidates.Add(candidate);
                    await _context.SaveChangesAsync();

                    TempData["Success"] = "Candidate created successfully.";
                    return RedirectToAction(nameof(Index));
                }
            }

            // If we got this far, something failed, redisplay form
            model.Categories = await _context.Categories.Where(c => c.IsActive).ToListAsync();
            model.Ranks = await _context.Ranks.OrderBy(r => r.DisplayOrder).ToListAsync();
            model.Airbases = await _context.Airbases.OrderBy(a => a.AirbaseId).ToListAsync();
            return View(model);
        }

        // GET: Candidate/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var candidate = await _context.Candidates.FindAsync(id);
            if (candidate == null)
            {
                return NotFound();
            }

            var viewModel = new CandidateEditViewModel
            {
                CandidateId = candidate.CandidateId,
                FullName = candidate.FullName,
                CategoryId = candidate.CategoryId,
                ServiceNumber = candidate.ServiceNumber,
                NationalIdNumber = candidate.NationalIdNumber,
                RankId = candidate.RankId,
                AirbaseId = candidate.AirbaseId,
                Department = candidate.Department,
                Major = candidate.Major,
                University = candidate.University,
                GraduationYear = candidate.GraduationYear,
                MarksGrade = candidate.MarksGrade,
                DateOfBirth = candidate.DateOfBirth,
                JobTitle = candidate.JobTitle,
                IsActive = candidate.IsActive,
                Categories = await _context.Categories.Where(c => c.IsActive).ToListAsync(),
                Ranks = await _context.Ranks.OrderBy(r => r.DisplayOrder).ToListAsync(),
                Airbases = await _context.Airbases.OrderBy(a => a.AirbaseId).ToListAsync()
            };

            return View(viewModel);
        }

        // POST: Candidate/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CandidateEditViewModel model)
        {
            if (id != model.CandidateId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check for duplicate service number (excluding current record)
                    var existingByServiceNumber = await _context.Candidates
                        .FirstOrDefaultAsync(c => c.ServiceNumber == model.ServiceNumber && c.CandidateId != id);
                    if (existingByServiceNumber != null)
                    {
                        ModelState.AddModelError("ServiceNumber", "Service number already exists.");
                    }

                    // Check for duplicate national ID (excluding current record)
                    var existingByNationalId = await _context.Candidates
                        .FirstOrDefaultAsync(c => c.NationalIdNumber == model.NationalIdNumber && c.CandidateId != id);
                    if (existingByNationalId != null)
                    {
                        ModelState.AddModelError("NationalIdNumber", "National ID number already exists.");
                    }

                    if (ModelState.IsValid)
                    {
                        var candidate = await _context.Candidates.FindAsync(id);
                        if (candidate == null)
                        {
                            return NotFound();
                        }

                        candidate.FullName = model.FullName;
                        candidate.CategoryId = model.CategoryId;
                        candidate.ServiceNumber = model.ServiceNumber;
                        candidate.NationalIdNumber = model.NationalIdNumber;
                        candidate.RankId = model.RankId;
                        candidate.AirbaseId = model.AirbaseId;
                        candidate.Department = model.Department;
                        candidate.Major = model.Major;
                        candidate.University = model.University;
                        candidate.GraduationYear = model.GraduationYear;
                        candidate.MarksGrade = model.MarksGrade;
                        candidate.DateOfBirth = model.DateOfBirth;
                        candidate.JobTitle = model.JobTitle;
                        candidate.IsActive = model.IsActive;
                        candidate.UpdatedAt = DateTime.UtcNow;

                        _context.Update(candidate);
                        await _context.SaveChangesAsync();

                        TempData["Success"] = "Candidate updated successfully.";
                        return RedirectToAction(nameof(Index));
                    }
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await CandidateExists(model.CandidateId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            // If we got this far, something failed, redisplay form
            model.Categories = await _context.Categories.Where(c => c.IsActive).ToListAsync();
            model.Ranks = await _context.Ranks.OrderBy(r => r.DisplayOrder).ToListAsync();
            model.Airbases = await _context.Airbases.OrderBy(a => a.AirbaseId).ToListAsync();
            return View(model);
        }

        // GET: Candidate/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var candidate = await _context.Candidates
                .Include(c => c.Category)
                .Include(c => c.Rank)
                .FirstOrDefaultAsync(c => c.CandidateId == id);

            if (candidate == null)
            {
                return NotFound();
            }

            var viewModel = new CandidateViewModel
            {
                CandidateId = candidate.CandidateId,
                FullName = candidate.FullName,
                ServiceNumber = candidate.ServiceNumber,
                NationalIdNumber = candidate.NationalIdNumber,
                CategoryId = candidate.CategoryId,
                CategoryName = candidate.Category.CategoryName,
                RankId = candidate.RankId,
                RankName = candidate.Rank.RankName,
                AirbaseId = candidate.AirbaseId,
                Department = candidate.Department,
                Major = candidate.Major,
                University = candidate.University,
                GraduationYear = candidate.GraduationYear,
                MarksGrade = candidate.MarksGrade,
                DateOfBirth = candidate.DateOfBirth,
                JobTitle = candidate.JobTitle,
                IsActive = candidate.IsActive,
                CreatedAt = candidate.CreatedAt,
                UpdatedAt = candidate.UpdatedAt
            };

            return View(viewModel);
        }

        // POST: Candidate/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var candidate = await _context.Candidates.FindAsync(id);
            if (candidate != null)
            {
                _context.Candidates.Remove(candidate);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Candidate deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Candidate/ToggleStatus/5
        [HttpPost]
        public async Task<IActionResult> ToggleStatus(int id)
        {
            var candidate = await _context.Candidates.FindAsync(id);
            if (candidate == null)
            {
                return Json(new { success = false, message = "Candidate not found." });
            }

            candidate.IsActive = !candidate.IsActive;
            candidate.UpdatedAt = DateTime.UtcNow;

            try
            {
                await _context.SaveChangesAsync();
                return Json(new { success = true, message = "Status updated successfully." });
            }
            catch (Exception)
            {
                return Json(new { success = false, message = "An error occurred while updating the status." });
            }
        }

        private async Task<bool> CandidateExists(int id)
        {
            return await _context.Candidates.AnyAsync(e => e.CandidateId == id);
        }
    }
}
