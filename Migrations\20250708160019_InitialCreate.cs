﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Ranks",
                columns: table => new
                {
                    RankId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    RankName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 255, nullable: true),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ranks", x => x.RankId);
                });

   migrationBuilder.CreateTable(
                name: "Airbases",
                columns: table => new
                {
                    AirbaseId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    AirbaseName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Airbases", x => x.AirbaseId);
                });
            migrationBuilder.CreateTable(
                name: "Roles",
                columns: table => new
                {
                    RoleId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    RoleName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 255, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.RoleId);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    UserId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ServiceNumber = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false),
                    Password = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    RankId = table.Column<int>(type: "INTEGER", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    LastLoginAt = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.UserId);
                    table.ForeignKey(
                        name: "FK_Users_Ranks_RankId",
                        column: x => x.RankId,
                        principalTable: "Ranks",
                        principalColumn: "RankId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "UserRoles",
                columns: table => new
                {
                    UserRoleId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    UserId = table.Column<int>(type: "INTEGER", nullable: false),
                    RoleId = table.Column<int>(type: "INTEGER", nullable: false),
                    AssignedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRoles", x => x.UserRoleId);
                    table.ForeignKey(
                        name: "FK_UserRoles_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "RoleId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserRoles_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Ranks",
                columns: new[] { "RankId", "CreatedAt", "Description", "DisplayOrder", "RankName" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(1009), "Senior Air Officer", 1, "Air Marshal" },
                    { 2, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(1968), "Senior Air Officer", 2, "Air Vice Marshal" },
                    { 3, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(1971), "Senior Air Officer", 3, "Air Commodore" },
                    { 4, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(1974), "Senior Officer", 4, "Group Captain" },
                    { 5, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2004), "Senior Officer", 5, "Wing Commander" },
                    { 6, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2005), "Officer", 6, "Squadron Leader" },
                    { 7, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2006), "Officer", 7, "Flight Lieutenant" },
                    { 8, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2007), "Junior Officer", 8, "Flying Officer" },
                    { 9, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2008), "Junior Officer", 9, "Pilot Officer" },
                    { 10, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2009), "Senior NCO", 10, "Warrant Officer" },
                    { 11, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2009), "NCO", 11, "Flight Sergeant" },
                    { 12, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2010), "NCO", 12, "Sergeant" },
                    { 13, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2011), "Junior NCO", 13, "Corporal" },
                    { 14, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2012), "Enlisted", 14, "Senior Aircraftman" },
                    { 15, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2013), "Enlisted", 15, "Leading Aircraftman" },
                    { 16, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(2014), "Enlisted", 16, "Aircraftman" }
                });

            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "RoleId", "CreatedAt", "Description", "RoleName" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(7896), "System Administrator with full access", "Admin" },
                    { 2, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(8618), "Evaluation Coordinator", "Coordinator" },
                    { 3, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(8621), "Personnel Evaluator", "Evaluator" },
                    { 4, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(8622), "Read-only access to evaluations", "Viewer" },
                    { 5, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(8622), "Human Resources Manager", "HR Manager" },
                    { 6, new DateTime(2025, 7, 8, 16, 0, 18, 792, DateTimeKind.Utc).AddTicks(8623), "Training and Development Officer", "Training Officer" }
                });

            migrationBuilder.InsertData(
                table: "Users",
                columns: new[] { "UserId", "CreatedAt", "IsActive", "LastLoginAt", "Password", "RankId", "ServiceNumber" },
                values: new object[] { 1, new DateTime(2025, 7, 8, 16, 0, 19, 26, DateTimeKind.Utc).AddTicks(1593), true, null, "$2a$11$HS9uUmY0ubh2MswZvxqwS.y/cH3d7RlorY1RLpeA4U/8Tx/taiiEW", 3, "RAFO001" });

            migrationBuilder.InsertData(
                table: "UserRoles",
                columns: new[] { "UserRoleId", "AssignedAt", "RoleId", "UserId" },
                values: new object[] { 1, new DateTime(2025, 7, 8, 16, 0, 19, 26, DateTimeKind.Utc).AddTicks(3168), 1, 1 });

            migrationBuilder.InsertData(
                table: "Airbases",
                columns: new[] { "AirbaseId", "AirbaseName" },
                values: new object[,]
                {
                    { 1, "قيادة سلاح الجو السلطاني العماني" },
                    { 2, "قاعدة غلا وأكاديمية السلطان قابوس الجوية" },
                    { 3, "قاعدة السيب الجوية" },
                    { 4, "قاعدة صلالة الجوية" },
                    { 5, "قاعدة المصنعة الجوية" },
                    { 6, "قاعدة مصيرة الجوية" },
                    { 7, "قاعدة أدم الجوية" },
                    { 8, "قاعدة ثمريت الجوية" },
                    { 9, "قاعدة خصب الجوية" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Ranks_RankName",
                table: "Ranks",
                column: "RankName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Roles_RoleName",
                table: "Roles",
                column: "RoleName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_RoleId",
                table: "UserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_UserId_RoleId",
                table: "UserRoles",
                columns: new[] { "UserId", "RoleId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_RankId",
                table: "Users",
                column: "RankId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_ServiceNumber",
                table: "Users",
                column: "ServiceNumber",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Airbases");

            migrationBuilder.DropTable(
                name: "UserRoles");

            migrationBuilder.DropTable(
                name: "Roles");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropTable(
                name: "Ranks");
        }
    }
}
