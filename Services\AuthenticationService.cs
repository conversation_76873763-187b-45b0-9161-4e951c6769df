using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models.Auth;

namespace RafoEvaluation.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AuthenticationService> _logger;

        public AuthenticationService(ApplicationDbContext context, ILogger<AuthenticationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<User?> AuthenticateAsync(string serviceNumber, string password)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.Rank)
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .FirstOrDefaultAsync(u => u.ServiceNumber == serviceNumber && u.IsActive);

                if (user == null)
                {
                    _logger.LogWarning("Authentication failed: User not found or inactive. ServiceNumber: {ServiceNumber}", serviceNumber);
                    return null;
                }

                if (!VerifyPassword(password, user.Password))
                {
                    _logger.LogWarning("Authentication failed: Invalid password. ServiceNumber: {ServiceNumber}", serviceNumber);
                    return null;
                }

                // Update last login time
                user.LastLoginAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                _logger.LogInformation("User authenticated successfully. ServiceNumber: {ServiceNumber}", serviceNumber);
                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authentication for ServiceNumber: {ServiceNumber}", serviceNumber);
                return null;
            }
        }

        public async Task<User?> GetUserByIdAsync(int userId)
        {
            return await _context.Users
                .Include(u => u.Rank)
                .Include(u => u.UserRoles)
                    .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.UserId == userId);
        }

        public async Task<User?> GetUserByServiceNumberAsync(string serviceNumber)
        {
            return await _context.Users
                .Include(u => u.Rank)
                .Include(u => u.UserRoles)
                    .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.ServiceNumber == serviceNumber);
        }

        public async Task<bool> CreateUserAsync(User user, string password, List<int> roleIds)
        {
            try
            {
                // Check if service number already exists
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.ServiceNumber == user.ServiceNumber);

                if (existingUser != null)
                {
                    _logger.LogWarning("User creation failed: ServiceNumber already exists. ServiceNumber: {ServiceNumber}", user.ServiceNumber);
                    return false;
                }

                // Hash the password
                user.Password = HashPassword(password);
                user.CreatedAt = DateTime.UtcNow;

                // Add user to context
                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                // Assign roles
                if (roleIds != null && roleIds.Any())
                {
                    foreach (var roleId in roleIds)
                    {
                        var userRole = new UserRole
                        {
                            UserId = user.UserId,
                            RoleId = roleId,
                            AssignedAt = DateTime.UtcNow
                        };
                        _context.UserRoles.Add(userRole);
                    }
                    await _context.SaveChangesAsync();
                }

                _logger.LogInformation("User created successfully. ServiceNumber: {ServiceNumber}", user.ServiceNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user. ServiceNumber: {ServiceNumber}", user.ServiceNumber);
                return false;
            }
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                _context.Users.Update(user);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user. UserId: {UserId}", user.UserId);
                return false;
            }
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("Password change failed: User not found. UserId: {UserId}", userId);
                    return false;
                }

                if (!VerifyPassword(currentPassword, user.Password))
                {
                    _logger.LogWarning("Password change failed: Current password incorrect. UserId: {UserId}", userId);
                    return false;
                }

                user.Password = HashPassword(newPassword);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Password changed successfully. UserId: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password. UserId: {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> ResetPasswordAsync(int userId, string newPassword)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("Password reset failed: User not found. UserId: {UserId}", userId);
                    return false;
                }

                user.Password = HashPassword(newPassword);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Password reset successfully. UserId: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting password. UserId: {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> ActivateUserAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return false;
                }

                user.IsActive = true;
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating user. UserId: {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> DeactivateUserAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return false;
                }

                user.IsActive = false;
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating user. UserId: {UserId}", userId);
                return false;
            }
        }

        public async Task<List<User>> GetAllUsersAsync()
        {
            return await _context.Users
                .Include(u => u.Rank)
                .Include(u => u.UserRoles)
                    .ThenInclude(ur => ur.Role)
                .OrderBy(u => u.Rank.DisplayOrder)
                .ThenBy(u => u.ServiceNumber)
                .ToListAsync();
        }

        public async Task<List<User>> GetActiveUsersAsync()
        {
            return await _context.Users
                .Include(u => u.Rank)
                .Include(u => u.UserRoles)
                    .ThenInclude(ur => ur.Role)
                .Where(u => u.IsActive)
                .OrderBy(u => u.Rank.DisplayOrder)
                .ThenBy(u => u.ServiceNumber)
                .ToListAsync();
        }

        public async Task<bool> AssignRoleAsync(int userId, int roleId)
        {
            try
            {
                // Check if the assignment already exists
                var existingAssignment = await _context.UserRoles
                    .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId);

                if (existingAssignment != null)
                {
                    return true; // Already assigned
                }

                var userRole = new UserRole
                {
                    UserId = userId,
                    RoleId = roleId,
                    AssignedAt = DateTime.UtcNow
                };

                _context.UserRoles.Add(userRole);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning role. UserId: {UserId}, RoleId: {RoleId}", userId, roleId);
                return false;
            }
        }

        public async Task<bool> RemoveRoleAsync(int userId, int roleId)
        {
            try
            {
                var userRole = await _context.UserRoles
                    .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId);

                if (userRole == null)
                {
                    return true; // Already removed
                }

                _context.UserRoles.Remove(userRole);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing role. UserId: {UserId}, RoleId: {RoleId}", userId, roleId);
                return false;
            }
        }

        public string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }
    }
}
