using RafoEvaluation.Models;
using RafoEvaluation.Models.Auth;
using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.ViewModels
{
    public class CandidateViewModel
    {
        public int CandidateId { get; set; }

        [Required]
        [Display(Name = "Full Name")]
        public string FullName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Category")]
        public int CategoryId { get; set; }

        [Required]
        [Display(Name = "Service Number")]
        public string ServiceNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "National ID Number")]
        public string NationalIdNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Rank")]
        public int RankId { get; set; }

        [Required]
        [Display(Name = "Airbase ID")]
        public int AirbaseId { get; set; }

        [Display(Name = "Department")]
        public string? Department { get; set; }

        [Display(Name = "Major")]
        public string? Major { get; set; }

        [Display(Name = "University")]
        public string? University { get; set; }

        [Display(Name = "Graduation Year")]
        public int? GraduationYear { get; set; }

        [Display(Name = "Marks Grade")]
        public string? MarksGrade { get; set; }

        [Required]
        [Display(Name = "Date of Birth")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [Display(Name = "Job Title")]
        public string? JobTitle { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation Properties for Display
        public string? CategoryName { get; set; }
        public string? RankName { get; set; }
        public int Age => DateTime.Now.Year - DateOfBirth.Year - (DateTime.Now.DayOfYear < DateOfBirth.DayOfYear ? 1 : 0);
    }

    public class CandidateCreateViewModel
    {
        [Required]
        [Display(Name = "Full Name")]
        public string FullName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Category")]
        public int CategoryId { get; set; }

        [Required]
        [Display(Name = "Service Number")]
        public string ServiceNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "National ID Number")]
        public string NationalIdNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Rank")]
        public int RankId { get; set; }

        [Required]
        [Display(Name = "Airbase ID")]
        public int AirbaseId { get; set; }

        [Display(Name = "Department")]
        public string? Department { get; set; }

        [Display(Name = "Major")]
        public string? Major { get; set; }

        [Display(Name = "University")]
        public string? University { get; set; }

        [Display(Name = "Graduation Year")]
        public int? GraduationYear { get; set; }

        [Display(Name = "Marks Grade")]
        public string? MarksGrade { get; set; }

        [Required]
        [Display(Name = "Date of Birth")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [Display(Name = "Job Title")]
        public string? JobTitle { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        // Lists for dropdowns
        public List<Category> Categories { get; set; } = new();
        public List<Rank> Ranks { get; set; } = new();
        public List<Airbase> Airbases { get; set; } = new();
    }

    public class CandidateEditViewModel
    {
        public int CandidateId { get; set; }

        [Required]
        [Display(Name = "Full Name")]
        public string FullName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Category")]
        public int CategoryId { get; set; }

        [Required]
        [Display(Name = "Service Number")]
        public string ServiceNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "National ID Number")]
        public string NationalIdNumber { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Rank")]
        public int RankId { get; set; }

        [Required]
        [Display(Name = "Airbase ID")]
        public int AirbaseId { get; set; }

        [Display(Name = "Department")]
        public string? Department { get; set; }

        [Display(Name = "Major")]
        public string? Major { get; set; }

        [Display(Name = "University")]
        public string? University { get; set; }

        [Display(Name = "Graduation Year")]
        public int? GraduationYear { get; set; }

        [Display(Name = "Marks Grade")]
        public string? MarksGrade { get; set; }

        [Required]
        [Display(Name = "Date of Birth")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [Display(Name = "Job Title")]
        public string? JobTitle { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        // Lists for dropdowns
        public List<Category> Categories { get; set; } = new();
        public List<Rank> Ranks { get; set; } = new();
        public List<Airbase> Airbases { get; set; } = new();
    }

    public class CandidateListViewModel
    {
        public List<CandidateViewModel> Candidates { get; set; } = new();
        public string SearchTerm { get; set; } = string.Empty;
        public int CategoryFilter { get; set; } = 0;
        public int RankFilter { get; set; } = 0;
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public int PageSize { get; set; } = 10;

        // Filter options
        public List<Category> Categories { get; set; } = new();
        public List<Rank> Ranks { get; set; } = new();
    }
}
