// Update database with new BCrypt hash for "1234567"
using Microsoft.Data.Sqlite;
using System;

string password = "1234567";
string newHash = "$2a$11$ef3G8GpupKKStOCT1sSdTOQ0wa.StUPaEYSt33Qge7I0kmbdNE5VS";
string connectionString = "Data Source=../rafo_evaluation.db";

try
{
    using var connection = new SqliteConnection(connectionString);
    connection.Open();

    using var command = connection.CreateCommand();
    command.CommandText = "UPDATE Users SET Password = @password WHERE ServiceNumber = 'RAFO001'";
    command.Parameters.AddWithValue("@password", newHash);

    int rowsAffected = command.ExecuteNonQuery();
    Console.WriteLine($"Password updated successfully. Rows affected: {rowsAffected}");

    // Verify the update worked
    using var verifyCommand = connection.CreateCommand();
    verifyCommand.CommandText = "SELECT Password FROM Users WHERE ServiceNumber = 'RAFO001'";
    string storedHash = verifyCommand.ExecuteScalar()?.ToString() ?? "";

    bool isValid = BCrypt.Net.BCrypt.Verify(password, storedHash);
    Console.WriteLine($"Verification test: {isValid}");
    Console.WriteLine($"Password '1234567' is now ready for use!");
}
catch (Exception ex)
{
    Console.WriteLine($"Error: {ex.Message}");
}
