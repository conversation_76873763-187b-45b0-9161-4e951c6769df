@model RafoEvaluation.ViewModels.CategoryViewModel
@{
    ViewData["Title"] = "Delete Category";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-trash me-2"></i>Delete Category
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning!</strong> Are you sure you want to delete this category? This action cannot be undone.
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>Category Name:</strong>
                                </div>
                                <div class="col-sm-9">
                                    @Model.CategoryName
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>Category Code:</strong>
                                </div>
                                <div class="col-sm-9">
                                    <span class="badge bg-secondary fs-6">@Model.CategoryCode</span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>Description:</strong>
                                </div>
                                <div class="col-sm-9">
                                    @if (!string.IsNullOrEmpty(Model.Description))
                                    {
                                        <p class="mb-0">@Model.Description</p>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">No description provided</span>
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>Status:</strong>
                                </div>
                                <div class="col-sm-9">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>Inactive
                                        </span>
                                    }
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-3">
                                    <strong>Created:</strong>
                                </div>
                                <div class="col-sm-9">
                                    @Model.CreatedAt.ToString("MMMM dd, yyyy 'at' h:mm tt")
                                </div>
                            </div>

                            @if (Model.UpdatedAt.HasValue)
                            {
                                <div class="row mb-3">
                                    <div class="col-sm-3">
                                        <strong>Last Updated:</strong>
                                    </div>
                                    <div class="col-sm-9">
                                        @Model.UpdatedAt.Value.ToString("MMMM dd, yyyy 'at' h:mm tt")
                                    </div>
                                </div>
                            }
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light border-danger">
                                <div class="card-body">
                                    <h6 class="card-title text-danger">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Deletion Impact
                                    </h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            Deleting this category will permanently remove it from the system. 
                                            If this category is referenced by other records, the deletion may fail.
                                        </small>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to List
                            </a>
                            <a asp-action="Details" asp-route-id="@Model.CategoryId" class="btn btn-info">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                        </div>
                        <form asp-action="Delete" method="post" class="d-inline">
                            <input type="hidden" asp-for="CategoryId" />
                            <button type="submit" class="btn btn-danger" 
                                    onclick="return confirm('Are you absolutely sure you want to delete this category?')">
                                <i class="fas fa-trash me-1"></i>Delete Category
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
