using Microsoft.Data.Sqlite;
using System;

class Program
{
    static void Main()
    {
        string connectionString = "Data Source=rafo_evaluation.db";
        
        var ranks = new[]
        {
            new { Id = 1, Name = "جندي", Order = 17, Description = "Military Rank" },
            new { Id = 2, Name = "نائب عريف", Order = 16, Description = "Military Rank" },
            new { Id = 3, Name = "عريف", Order = 15, Description = "Military Rank" },
            new { Id = 4, Name = "رقيب", Order = 14, Description = "Military Rank" },
            new { Id = 5, Name = "رقيب أول", Order = 13, Description = "Military Rank" },
            new { Id = 6, Name = "وكيل", Order = 12, Description = "Military Rank" },
            new { Id = 7, Name = "وكيل أول", Order = 11, Description = "Military Rank" },
            new { Id = 8, Name = "ضابط مرشح", Order = 10, Description = "Military Rank" },
            new { Id = 9, Name = "ملازم ثاني", Order = 9, Description = "Military Rank" },
            new { Id = 10, Name = "ملازم أول", Order = 8, Description = "Military Rank" },
            new { Id = 11, Name = "نقيب", Order = 7, Description = "Military Rank" },
            new { Id = 12, Name = "رائد", Order = 6, Description = "Military Rank" },
            new { Id = 13, Name = "مقدم", Order = 5, Description = "Military Rank" },
            new { Id = 14, Name = "عقيد", Order = 4, Description = "Military Rank" },
            new { Id = 15, Name = "عميد", Order = 3, Description = "Military Rank" },
            new { Id = 16, Name = "لواء", Order = 2, Description = "Military Rank" },
            new { Id = 17, Name = "فريق", Order = 1, Description = "Military Rank" },
            new { Id = 18, Name = "مدني درجة 16", Order = 34, Description = "Civilian Rank" },
            new { Id = 19, Name = "مدني درجة 15", Order = 33, Description = "Civilian Rank" },
            new { Id = 20, Name = "مدني درجة 14", Order = 32, Description = "Civilian Rank" },
            new { Id = 21, Name = "مدني درجة 13", Order = 31, Description = "Civilian Rank" },
            new { Id = 22, Name = "مدني درجة 12", Order = 30, Description = "Civilian Rank" },
            new { Id = 23, Name = "مدني درجة 11", Order = 29, Description = "Civilian Rank" },
            new { Id = 24, Name = "مدني درجة 10", Order = 28, Description = "Civilian Rank" },
            new { Id = 25, Name = "مدني درجة 9", Order = 27, Description = "Civilian Rank" },
            new { Id = 26, Name = "ضابط مدني د8", Order = 26, Description = "Civilian Officer Rank" },
            new { Id = 27, Name = "ضابط مدني د9", Order = 25, Description = "Civilian Officer Rank" },
            new { Id = 28, Name = "ضابط مدني د7", Order = 24, Description = "Civilian Officer Rank" },
            new { Id = 29, Name = "ضابط مدني د6", Order = 23, Description = "Civilian Officer Rank" },
            new { Id = 30, Name = "ضابط مدني د5", Order = 22, Description = "Civilian Officer Rank" },
            new { Id = 31, Name = "ضابط مدني د4", Order = 21, Description = "Civilian Officer Rank" },
            new { Id = 32, Name = "ضابط مدني د3", Order = 20, Description = "Civilian Officer Rank" },
            new { Id = 33, Name = "ضابط مدني د2", Order = 19, Description = "Civilian Officer Rank" },
            new { Id = 34, Name = "ضابط مدني د1", Order = 18, Description = "Civilian Officer Rank" }
        };

        try
        {
            using var connection = new SqliteConnection(connectionString);
            connection.Open();
            
            // Update existing ranks (1-16)
            for (int i = 0; i < 16; i++)
            {
                var rank = ranks[i];
                using var command = connection.CreateCommand();
                command.CommandText = "UPDATE Ranks SET RankName = @name, DisplayOrder = @order, Description = @desc WHERE RankId = @id";
                command.Parameters.AddWithValue("@name", rank.Name);
                command.Parameters.AddWithValue("@order", rank.Order);
                command.Parameters.AddWithValue("@desc", rank.Description);
                command.Parameters.AddWithValue("@id", rank.Id);
                
                int rowsAffected = command.ExecuteNonQuery();
                Console.WriteLine($"Updated rank {rank.Id}: {rank.Name} (Order: {rank.Order}) - Rows affected: {rowsAffected}");
            }
            
            // Insert new ranks (17-34)
            for (int i = 16; i < ranks.Length; i++)
            {
                var rank = ranks[i];
                using var command = connection.CreateCommand();
                command.CommandText = "INSERT OR REPLACE INTO Ranks (RankId, RankName, DisplayOrder, Description, CreatedAt) VALUES (@id, @name, @order, @desc, @created)";
                command.Parameters.AddWithValue("@id", rank.Id);
                command.Parameters.AddWithValue("@name", rank.Name);
                command.Parameters.AddWithValue("@order", rank.Order);
                command.Parameters.AddWithValue("@desc", rank.Description);
                command.Parameters.AddWithValue("@created", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"));
                
                int rowsAffected = command.ExecuteNonQuery();
                Console.WriteLine($"Inserted rank {rank.Id}: {rank.Name} (Order: {rank.Order}) - Rows affected: {rowsAffected}");
            }
            
            // Update admin user to use the highest military rank (فريق - RankId 17)
            using var userCommand = connection.CreateCommand();
            userCommand.CommandText = "UPDATE Users SET RankId = 17 WHERE ServiceNumber = 'RAFO001'";
            int userRowsAffected = userCommand.ExecuteNonQuery();
            Console.WriteLine($"Updated admin user rank to فريق (RankId 17) - Rows affected: {userRowsAffected}");
            
            Console.WriteLine("\n✅ All ranks updated successfully!");
            Console.WriteLine("📊 Total ranks: 34 (17 Military + 17 Civilian)");
            Console.WriteLine("👤 Admin user rank updated to: فريق (highest military rank)");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
    }
}
