using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Models.Auth;
using RafoEvaluation.Models;

namespace RafoEvaluation.Data
{
    public class ApplicationDbContext : DbContext
    {
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseSqlite("Data Source=rafo_evaluation.db");
            }
            
            // Suppress the pending model changes warning for seed data
            optionsBuilder.ConfigureWarnings(warnings =>
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.PendingModelChangesWarning));
        }

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<User> Users { get; set; }
        public DbSet<Rank> Ranks { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Candidate> Candidates { get; set; }
        public DbSet<Airbase> Airbases { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.UserId);
                
                entity.Property(e => e.ServiceNumber)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.HasIndex(e => e.ServiceNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Users_ServiceNumber");

                entity.Property(e => e.Password)
                    .IsRequired()
                    .HasMaxLength(255);

                entity.Property(e => e.IsActive)
                    .HasDefaultValue(true);

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("datetime('now')");

                // Foreign key relationship with Rank
                entity.HasOne(e => e.Rank)
                    .WithMany(r => r.Users)
                    .HasForeignKey(e => e.RankId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Rank entity
            modelBuilder.Entity<Rank>(entity =>
            {
                entity.HasKey(e => e.RankId);
                
                entity.Property(e => e.RankName)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("datetime('now')");

                entity.HasIndex(e => e.RankName)
                    .IsUnique()
                    .HasDatabaseName("IX_Ranks_RankName");
            });

            // Configure Role entity
            modelBuilder.Entity<Role>(entity =>
            {
                entity.HasKey(e => e.RoleId);
                
                entity.Property(e => e.RoleName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("datetime('now')");

                entity.HasIndex(e => e.RoleName)
                    .IsUnique()
                    .HasDatabaseName("IX_Roles_RoleName");
            });

            // Configure UserRole entity (Many-to-Many junction table)
            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.HasKey(e => e.UserRoleId);

                // Alternative approach: Composite key
                // entity.HasKey(e => new { e.UserId, e.RoleId });

                entity.Property(e => e.AssignedAt)
                    .HasDefaultValueSql("datetime('now')");

                // Foreign key relationships
                entity.HasOne(e => e.User)
                    .WithMany(u => u.UserRoles)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Role)
                    .WithMany(r => r.UserRoles)
                    .HasForeignKey(e => e.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Unique constraint to prevent duplicate user-role assignments
                entity.HasIndex(e => new { e.UserId, e.RoleId })
                    .IsUnique()
                    .HasDatabaseName("IX_UserRoles_UserId_RoleId");
            });

            // Configure Category entity
            modelBuilder.Entity<Category>(entity =>
            {
                entity.HasKey(e => e.CategoryId);
                entity.Property(e => e.CategoryName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            });

            // Configure Airbase entity
            modelBuilder.Entity<Airbase>(entity =>
            {
                entity.HasKey(e => e.AirbaseId);
                entity.Property(e => e.AirbaseName).IsRequired().HasMaxLength(100);
            });

            // Configure Candidate entity
            modelBuilder.Entity<Candidate>(entity =>
            {
                entity.HasKey(e => e.CandidateId);
                
                entity.Property(e => e.FullName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ServiceNumber)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.NationalIdNumber)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Department)
                    .HasMaxLength(200);

                entity.Property(e => e.Major)
                    .HasMaxLength(200);

                entity.Property(e => e.University)
                    .HasMaxLength(200);

                entity.Property(e => e.MarksGrade)
                    .HasMaxLength(10);

                entity.Property(e => e.JobTitle)
                    .HasMaxLength(200);

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("datetime('now')");

                entity.Property(e => e.IsActive)
                    .HasDefaultValue(true);

                // Indexes
                entity.HasIndex(e => e.ServiceNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Candidates_ServiceNumber");

                entity.HasIndex(e => e.NationalIdNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Candidates_NationalIdNumber");

                entity.HasIndex(e => e.FullName)
                    .HasDatabaseName("IX_Candidates_FullName");

                // Foreign key relationships
                entity.HasOne(e => e.Category)
                    .WithMany()
                    .HasForeignKey(e => e.CategoryId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Rank)
                    .WithMany()
                    .HasForeignKey(e => e.RankId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Airbase)
                    .WithMany(a => a.Candidates)
                    .HasForeignKey(e => e.AirbaseId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed Ranks
            modelBuilder.Entity<Rank>().HasData(
                new Rank { RankId = 1, RankName = "Air Marshal", Description = "Senior Air Officer", DisplayOrder = 1 },
                new Rank { RankId = 2, RankName = "Air Vice Marshal", Description = "Senior Air Officer", DisplayOrder = 2 },
                new Rank { RankId = 3, RankName = "Air Commodore", Description = "Senior Air Officer", DisplayOrder = 3 },
                new Rank { RankId = 4, RankName = "Group Captain", Description = "Senior Officer", DisplayOrder = 4 },
                new Rank { RankId = 5, RankName = "Wing Commander", Description = "Senior Officer", DisplayOrder = 5 },
                new Rank { RankId = 6, RankName = "Squadron Leader", Description = "Officer", DisplayOrder = 6 },
                new Rank { RankId = 7, RankName = "Flight Lieutenant", Description = "Officer", DisplayOrder = 7 },
                new Rank { RankId = 8, RankName = "Flying Officer", Description = "Junior Officer", DisplayOrder = 8 },
                new Rank { RankId = 9, RankName = "Pilot Officer", Description = "Junior Officer", DisplayOrder = 9 },
                new Rank { RankId = 10, RankName = "Warrant Officer", Description = "Senior NCO", DisplayOrder = 10 },
                new Rank { RankId = 11, RankName = "Flight Sergeant", Description = "NCO", DisplayOrder = 11 },
                new Rank { RankId = 12, RankName = "Sergeant", Description = "NCO", DisplayOrder = 12 },
                new Rank { RankId = 13, RankName = "Corporal", Description = "Junior NCO", DisplayOrder = 13 },
                new Rank { RankId = 14, RankName = "Senior Aircraftman", Description = "Enlisted", DisplayOrder = 14 },
                new Rank { RankId = 15, RankName = "Leading Aircraftman", Description = "Enlisted", DisplayOrder = 15 },
                new Rank { RankId = 16, RankName = "Aircraftman", Description = "Enlisted", DisplayOrder = 16 }
            );

            // Seed Roles
            modelBuilder.Entity<Role>().HasData(
                new Role { RoleId = 1, RoleName = "Admin", Description = "System Administrator with full access" },
                new Role { RoleId = 2, RoleName = "Coordinator", Description = "Evaluation Coordinator" },
                new Role { RoleId = 3, RoleName = "Evaluator", Description = "Personnel Evaluator" },
                new Role { RoleId = 4, RoleName = "Viewer", Description = "Read-only access to evaluations" },
                new Role { RoleId = 5, RoleName = "HR Manager", Description = "Human Resources Manager" },
                new Role { RoleId = 6, RoleName = "Training Officer", Description = "Training and Development Officer" }
            );

            // Seed a default admin user
            // Password is "Admin123!" - BCrypt hashed
            var hashedPassword = "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi";
            
            modelBuilder.Entity<User>().HasData(
                new User 
                { 
                    UserId = 1, 
                    ServiceNumber = "RAFO001", 
                    Password = hashedPassword,
                    RankId = 3, // Air Commodore
                    IsActive = true,
                    CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                }
            );

            // Assign admin role to default user
            modelBuilder.Entity<UserRole>().HasData(
                new UserRole 
                { 
                    UserRoleId = 1, 
                    UserId = 1, 
                    RoleId = 1, // Admin role
                    AssignedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                }
            );

            // Seed Categories
            modelBuilder.Entity<Category>().HasData(
                new Category { CategoryId = 1, CategoryName = "المرشحيين الطيارين", Description = "فئة المرشحين للالتحاق بسلاح الجو السلطاني العماني كمرشحين طيارين.", CategoryCode = "CAT-PLT", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
                new Category { CategoryId = 2, CategoryName = "المرشحيين الجامعيين العسكريين", Description = "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط عسكريين جامعيين", CategoryCode = "CAT-MUG", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
                new Category { CategoryId = 3, CategoryName = "المرشحيين الجامعيين المدنيين", Description = "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط مدنيين جامعيين", CategoryCode = "CAT-CUG", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
                new Category { CategoryId = 4, CategoryName = "ضباط الخدمة المحدودة", Description = "ضباط الصف من ذوي الخدمة المحدودة وسسبق لهم العمل في السلاح وهم برتبة وكيل فأعلى", CategoryCode = "CAT-LSO", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
                new Category { CategoryId = 5, CategoryName = "ضباط الصف ( رقباء / عرفاء)", Description = "ضباط صف برتبة رقيب أو عريف سبق لهم العمل بالسلاح", CategoryCode = "CAT-NCO", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
                new Category { CategoryId = 6, CategoryName = "ضباط الصف الكلية التقنية العسكرية", Description = "ضباط صف خريجوا الكلية العسكرية التقنية", CategoryCode = "CAT-TCN", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) },
                new Category { CategoryId = 7, CategoryName = "ضباط الصف المدنيين للترفيع", Description = "ضباط صف مدنيين مرشحين للترقية بالصفة المدنية", CategoryCode = "CAT-CNP", IsActive = true, CreatedAt = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc) }
            );

            // Seed Airbases
            modelBuilder.Entity<Airbase>().HasData(
                new Airbase { AirbaseId = 1, AirbaseName = "قيادة سلاح الجو السلطاني العماني" },
                new Airbase { AirbaseId = 2, AirbaseName = "قاعدة غلا وأكاديمية السلطان قابوس الجوية" },
                new Airbase { AirbaseId = 3, AirbaseName = "قاعدة السيب الجوية" },
                new Airbase { AirbaseId = 4, AirbaseName = "قاعدة صلالة الجوية" },
                new Airbase { AirbaseId = 5, AirbaseName = "قاعدة المصنعة الجوية" },
                new Airbase { AirbaseId = 6, AirbaseName = "قاعدة مصيرة الجوية" },
                new Airbase { AirbaseId = 7, AirbaseName = "قاعدة أدم الجوية" },
                new Airbase { AirbaseId = 8, AirbaseName = "قاعدة ثمريت الجوية" },
                new Airbase { AirbaseId = 9, AirbaseName = "قاعدة خصب الجوية" }
            );
        }
    }
}
