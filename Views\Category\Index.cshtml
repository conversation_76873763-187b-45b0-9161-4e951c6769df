@model RafoEvaluation.ViewModels.CategoryListViewModel
@{
    ViewData["Title"] = "الفئات";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="fas fa-list-alt breadcrumb-icon"></i>
                    إدارة الفئات
                </h1>
                <p class="page-subtitle">إدارة فئات المرشحين والتخصصات</p>
            </div>
            <div class="col-md-4">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-list-alt"></i> الفئات
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-list-alt ms-2"></i>إدارة الفئات
                    </h3>
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus ms-1"></i>إضافة فئة جديدة
                    </a>
                </div>
                <div class="card-body">
                    <!-- نموذج البحث -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" name="searchTerm" value="@Model.SearchTerm"
                                           class="form-control" placeholder="البحث بالاسم، الرمز، أو الوصف...">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                @if (!string.IsNullOrEmpty(Model.SearchTerm))
                                {
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-times ms-1"></i>مسح
                                    </a>
                                }
                            </div>
                        </div>
                    </form>

                    <!-- معلومات النتائج -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="text-muted mb-0">
                                عرض @((Model.CurrentPage - 1) * Model.PageSize + 1) إلى
                                @(Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCount)) من
                                @Model.TotalCount إدخال
                                @if (!string.IsNullOrEmpty(Model.SearchTerm))
                                {
                                    <span>(مُرشح من إجمالي الإدخالات)</span>
                                }
                            </p>
                        </div>
                    </div>

                    <!-- جدول الفئات -->
                    @if (Model.Categories.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الرمز</th>
                                        <th>الاسم</th>
                                        <th>الوصف</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.Categories)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">@item.CategoryCode</span>
                                            </td>
                                            <td>
                                                <strong>@item.CategoryName</strong>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(item.Description))
                                                {
                                                    <span class="text-truncate d-inline-block" style="max-width: 300px;" 
                                                          title="@item.Description">
                                                        @item.Description
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted fst-italic">لا يوجد وصف</span>
                                                }
                                            </td>
                                            <td>
                                                @if (item.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    @item.CreatedAt.ToString("MMM dd, yyyy")
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.CategoryId"
                                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.CategoryId"
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                                            onclick="toggleStatus(@item.CategoryId, '@item.CategoryName')"
                                                            title="تبديل الحالة">
                                                        <i class="fas fa-toggle-@(item.IsActive ? "on" : "off")"></i>
                                                    </button>
                                                    <a asp-action="Delete" asp-route-id="@item.CategoryId"
                                                       class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- التنقل بين الصفحات -->
                        @if (Model.TotalPages > 1)
                        {
                            <nav aria-label="تنقل الفئات">
                                <ul class="pagination justify-content-center">
                                    <!-- السابق -->
                                    <li class="page-item @(Model.CurrentPage == 1 ? "disabled" : "")">
                                        <a class="page-link" asp-action="Index"
                                           asp-route-page="@(Model.CurrentPage - 1)"
                                           asp-route-searchTerm="@Model.SearchTerm">
                                            <i class="fas fa-chevron-right"></i> السابق
                                        </a>
                                    </li>

                                    <!-- Page Numbers -->
                                    @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                            <a class="page-link" asp-action="Index" 
                                               asp-route-page="@i" 
                                               asp-route-searchTerm="@Model.SearchTerm">
                                                @i
                                            </a>
                                        </li>
                                    }

                                    <!-- التالي -->
                                    <li class="page-item @(Model.CurrentPage == Model.TotalPages ? "disabled" : "")">
                                        <a class="page-link" asp-action="Index"
                                           asp-route-page="@(Model.CurrentPage + 1)"
                                           asp-route-searchTerm="@Model.SearchTerm">
                                            التالي <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لم يتم العثور على فئات</h4>
                            @if (!string.IsNullOrEmpty(Model.SearchTerm))
                            {
                                <p class="text-muted">
                                    لا توجد فئات تطابق معايير البحث الخاصة بك.
                                    <a asp-action="Index" class="text-decoration-none">مسح البحث</a> لرؤية جميع الفئات.
                                </p>
                            }
                            else
                            {
                                <p class="text-muted">
                                    ابدأ بـ <a asp-action="Create" class="text-decoration-none">إنشاء فئتك الأولى</a>.
                                </p>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript لتبديل الحالة -->
<script>
    function toggleStatus(categoryId, categoryName) {
        if (confirm(`هل أنت متأكد من أنك تريد تبديل حالة "${categoryName}"؟`)) {
            fetch(`/Category/ToggleStatus/${categoryId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إظهار إشعار نجاح
                    showNotification('تم تحديث حالة الفئة بنجاح!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification(data.message || 'حدث خطأ أثناء تحديث الحالة.', 'error');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showNotification('حدث خطأ أثناء تحديث الحالة.', 'error');
            });
        }
    }
</script>
