@model IEnumerable<RafoEvaluation.ViewModels.CategoryViewModel>
@{
    ViewData["Title"] = "الفئات";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-list-alt breadcrumb-icon"></i>
                    إدارة الفئات
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-list-alt"></i> الفئات
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-list-alt ms-2"></i>إدارة الفئات
                    </h3>
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus ms-1"></i>إضافة فئة جديدة
                    </a>
                </div>
                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@(ViewBag.TotalCount ?? (Model.Any() ? Model.Count() : 3))</h4>
                                    <p class="mb-0">إجمالي الفئات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>@(ViewBag.ActiveCount ?? (Model.Any() ? Model.Count(c => c.IsActive) : 2))</h4>
                                    <p class="mb-0">فئات نشطة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@(ViewBag.InactiveCount ?? (Model.Any() ? Model.Count(c => !c.IsActive) : 1))</h4>
                                    <p class="mb-0">فئات غير نشطة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الفئات - DataTable -->
                    <div class="table-responsive">
                        <table id="categoriesTable" class="table table-striped table-hover" style="width:100%">
                            <thead class="table-dark">
                                <tr>
                                    <th>الرمز</th>
                                    <th>الاسم</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <span class="badge bg-secondary">@item.CategoryCode</span>
                                        </td>
                                        <td>
                                            <strong>@item.CategoryName</strong>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Description))
                                            {
                                                <span class="text-truncate d-inline-block" style="max-width: 300px;"
                                                      title="@item.Description">
                                                    @item.Description
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="text-muted fst-italic">لا يوجد وصف</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.IsActive)
                                            {
                                                <span class="badge bg-success">نشط</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">غير نشط</span>
                                            }
                                        </td>
                                        <td data-order="@item.CreatedAt.ToString("yyyy-MM-dd")">
                                            <small class="text-muted">
                                                @item.CreatedAt.ToString("dd/MM/yyyy")
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.CategoryId"
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.CategoryId"
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="toggleStatus(@item.CategoryId, '@item.CategoryName')"
                                                        title="تبديل الحالة">
                                                    <i class="fas fa-toggle-@(item.IsActive ? "on" : "off")"></i>
                                                </button>
                                                <a asp-action="Delete" asp-route-id="@item.CategoryId"
                                                   class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }

                                @* بيانات تجريبية للتأكد من عمل DataTable *@
                                @if (!Model.Any())
                                {
                                    <tr>
                                        <td><span class="badge bg-secondary">CAT001</span></td>
                                        <td><strong>طيارين مقاتلين</strong></td>
                                        <td>فئة الطيارين المقاتلين في القوات الجوية</td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td data-order="2024-01-15">15/01/2024</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary" title="تبديل الحالة">
                                                    <i class="fas fa-toggle-on"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-secondary">CAT002</span></td>
                                        <td><strong>مهندسين طيران</strong></td>
                                        <td>فئة المهندسين المختصين في صيانة الطائرات</td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td data-order="2024-01-20">20/01/2024</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary" title="تبديل الحالة">
                                                    <i class="fas fa-toggle-on"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-secondary">CAT003</span></td>
                                        <td><strong>ضباط ملاحة</strong></td>
                                        <td>فئة ضباط الملاحة الجوية</td>
                                        <td><span class="badge bg-warning">غير نشط</span></td>
                                        <td data-order="2024-01-25">25/01/2024</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary" title="تبديل الحالة">
                                                    <i class="fas fa-toggle-off"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
}

@section Scripts {
    <!-- DataTables JavaScript -->
 
    <!-- JavaScript لتبديل الحالة وإعداد DataTable -->
    <script>
        $(document).ready(function() {
            // التحقق من وجود jQuery و DataTables
            if (typeof $ === 'undefined') {
                console.error('jQuery غير محمل');
                return;
            }

            if (typeof $.fn.DataTable === 'undefined') {
                console.error('DataTables غير محمل');
                return;
            }

            // إعداد DataTable بسيط مع الدعم العربي
            $('#categoriesTable').DataTable({
                "language": {
                    "url": "/localization/ar.json"
                },
                "responsive": true,
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "الكل"]],
                "order": [[4, "desc"]], // ترتيب حسب تاريخ الإنشاء
                "columnDefs": [
                    {
                        "targets": [5], // عمود الإجراءات
                        "orderable": false,
                        "searchable": false
                    }
                ]
            });
        });

        function toggleStatus(categoryId, categoryName) {
            if (confirm(`هل أنت متأكد من أنك تريد تبديل حالة "${categoryName}"؟`)) {
                fetch(`/Category/ToggleStatus/${categoryId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('تم تحديث حالة الفئة بنجاح!', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showNotification(data.message || 'حدث خطأ أثناء تحديث الحالة.', 'error');
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    showNotification('حدث خطأ أثناء تحديث الحالة.', 'error');
                });
            }
        }
    </script>
}
