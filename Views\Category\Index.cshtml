@model RafoEvaluation.ViewModels.CategoryListViewModel
@{
    ViewData["Title"] = "الفئات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-list-alt ms-2"></i>إدارة الفئات
                    </h3>
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus ms-1"></i>إضافة فئة جديدة
                    </a>
                </div>
                <div class="card-body">
                    <!-- نموذج البحث -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" name="searchTerm" value="@Model.SearchTerm"
                                           class="form-control" placeholder="البحث بالاسم، الرمز، أو الوصف...">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                @if (!string.IsNullOrEmpty(Model.SearchTerm))
                                {
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-times ms-1"></i>مسح
                                    </a>
                                }
                            </div>
                        </div>
                    </form>

                    <!-- Results Info -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="text-muted mb-0">
                                Showing @((Model.CurrentPage - 1) * Model.PageSize + 1) to 
                                @(Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCount)) of 
                                @Model.TotalCount entries
                                @if (!string.IsNullOrEmpty(Model.SearchTerm))
                                {
                                    <span>(filtered from total entries)</span>
                                }
                            </p>
                        </div>
                    </div>

                    <!-- Categories Table -->
                    @if (Model.Categories.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Code</th>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.Categories)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">@item.CategoryCode</span>
                                            </td>
                                            <td>
                                                <strong>@item.CategoryName</strong>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(item.Description))
                                                {
                                                    <span class="text-truncate d-inline-block" style="max-width: 300px;" 
                                                          title="@item.Description">
                                                        @item.Description
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted fst-italic">No description</span>
                                                }
                                            </td>
                                            <td>
                                                @if (item.IsActive)
                                                {
                                                    <span class="badge bg-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    @item.CreatedAt.ToString("MMM dd, yyyy")
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.CategoryId" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.CategoryId" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="toggleStatus(@item.CategoryId, '@item.CategoryName')" 
                                                            title="Toggle Status">
                                                        <i class="fas fa-toggle-@(item.IsActive ? "on" : "off")"></i>
                                                    </button>
                                                    <a asp-action="Delete" asp-route-id="@item.CategoryId" 
                                                       class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (Model.TotalPages > 1)
                        {
                            <nav aria-label="Categories pagination">
                                <ul class="pagination justify-content-center">
                                    <!-- Previous -->
                                    <li class="page-item @(Model.CurrentPage == 1 ? "disabled" : "")">
                                        <a class="page-link" asp-action="Index" 
                                           asp-route-page="@(Model.CurrentPage - 1)" 
                                           asp-route-searchTerm="@Model.SearchTerm">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    </li>

                                    <!-- Page Numbers -->
                                    @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                            <a class="page-link" asp-action="Index" 
                                               asp-route-page="@i" 
                                               asp-route-searchTerm="@Model.SearchTerm">
                                                @i
                                            </a>
                                        </li>
                                    }

                                    <!-- Next -->
                                    <li class="page-item @(Model.CurrentPage == Model.TotalPages ? "disabled" : "")">
                                        <a class="page-link" asp-action="Index" 
                                           asp-route-page="@(Model.CurrentPage + 1)" 
                                           asp-route-searchTerm="@Model.SearchTerm">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No categories found</h4>
                            @if (!string.IsNullOrEmpty(Model.SearchTerm))
                            {
                                <p class="text-muted">
                                    No categories match your search criteria. 
                                    <a asp-action="Index" class="text-decoration-none">Clear search</a> to see all categories.
                                </p>
                            }
                            else
                            {
                                <p class="text-muted">
                                    Get started by <a asp-action="Create" class="text-decoration-none">creating your first category</a>.
                                </p>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Toggle Status -->
<script>
    function toggleStatus(categoryId, categoryName) {
        if (confirm(`Are you sure you want to toggle the status of "${categoryName}"?`)) {
            fetch(`/Category/ToggleStatus/${categoryId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'An error occurred while updating the status.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the status.');
            });
        }
    }
</script>
