@model IEnumerable<RafoEvaluation.ViewModels.CategoryViewModel>
@{
    ViewData["Title"] = "الفئات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <!-- Header مدمج مع Breadcrumb -->
                <div class="breadcrumb-card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="page-title me-3">
                            <i class="fas fa-list-alt breadcrumb-icon"></i>
                            إدارة الفئات
                        </h5>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a asp-action="Index" asp-controller="Home">
                                        <i class="fas fa-home"></i> الرئيسية
                                    </a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    <i class="fas fa-list-alt"></i> الفئات
                                </li>
                            </ol>
                        </nav>
                    </div>
                    <a asp-action="Create" class="btn btn-light btn-sm">
                        <i class="fas fa-plus me-1"></i>
                        إضافة فئة جديدة
                    </a>
                </div>
                <div class="card-body">
                    <!-- جدول الفئات - DataTable -->
                    <div class="table-responsive">
                        <table id="categoriesTable" class="table table-striped table-hover" style="width:100%">
                            <thead class="table-dark">
                                <tr>
                                    <th>الرمز</th>
                                    <th>الاسم</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td><span class="badge bg-primary">@item.CategoryCode</span></td>
                                        <td>@item.CategoryName</td>
                                        <td>@item.Description</td>
                                        <td>
                                            @if (item.IsActive)
                                            {
                                                <span class="badge bg-success">Active</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">Inactive</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.CategoryId" 
                                                   class="btn btn-sm btn-outline-info" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>



@section Scripts {
    <!-- DataTables JavaScript -->
 
    <!-- JavaScript لتبديل الحالة وإعداد DataTable -->
    <script>
        $(document).ready(function() {
            // التحقق من وجود jQuery و DataTables
            if (typeof $ === 'undefined') {
                console.error('jQuery غير محمل');
                return;
            }

            if (typeof $.fn.DataTable === 'undefined') {
                console.error('DataTables غير محمل');
                return;
            }

            // إعداد DataTable بسيط مع الدعم العربي
            $('#categoriesTable').DataTable({
                "language": {
                    "url": "/localization/ar.json"
                },
                "responsive": true,
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "الكل"]],
                "order": [[4, "desc"]], // ترتيب حسب تاريخ الإنشاء
                "columnDefs": [
                    {
                        "targets": [5], // عمود الإجراءات
                        "orderable": false,
                        "searchable": false
                    }
                ]
            });
        });

        function toggleStatus(categoryId, categoryName) {
            if (confirm(`هل أنت متأكد من أنك تريد تبديل حالة "${categoryName}"؟`)) {
                fetch(`/Category/ToggleStatus/${categoryId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('تم تحديث حالة الفئة بنجاح!', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showNotification(data.message || 'حدث خطأ أثناء تحديث الحالة.', 'error');
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    showNotification('حدث خطأ أثناء تحديث الحالة.', 'error');
                });
            }
        }
    </script>
}
