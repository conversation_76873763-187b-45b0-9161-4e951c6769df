﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace RafoEvaluation.Migrations
{
    /// <inheritdoc />
    public partial class NewMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Airbases",
                columns: table => new
                {
                    AirbaseId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    AirbaseName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Airbases", x => x.AirbaseId);
                });

            migrationBuilder.CreateTable(
                name: "Categories",
                columns: table => new
                {
                    CategoryId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CategoryName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    CategoryCode = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Categories", x => x.CategoryId);
                });

            migrationBuilder.CreateTable(
                name: "Ranks",
                columns: table => new
                {
                    RankId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    RankName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 255, nullable: true),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ranks", x => x.RankId);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                columns: table => new
                {
                    RoleId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    RoleName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 255, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.RoleId);
                });

            migrationBuilder.CreateTable(
                name: "Candidates",
                columns: table => new
                {
                    CandidateId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    FullName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    CategoryId = table.Column<int>(type: "INTEGER", nullable: false),
                    ServiceNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    NationalIdNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    RankId = table.Column<int>(type: "INTEGER", nullable: false),
                    AirbaseId = table.Column<int>(type: "INTEGER", nullable: false),
                    Department = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    Major = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    University = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    GraduationYear = table.Column<int>(type: "INTEGER", nullable: true),
                    MarksGrade = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    DateOfBirth = table.Column<DateTime>(type: "TEXT", nullable: false),
                    JobTitle = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Candidates", x => x.CandidateId);
                    table.ForeignKey(
                        name: "FK_Candidates_Airbases_AirbaseId",
                        column: x => x.AirbaseId,
                        principalTable: "Airbases",
                        principalColumn: "AirbaseId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Candidates_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "Categories",
                        principalColumn: "CategoryId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Candidates_Ranks_RankId",
                        column: x => x.RankId,
                        principalTable: "Ranks",
                        principalColumn: "RankId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    UserId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ServiceNumber = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false),
                    Password = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    RankId = table.Column<int>(type: "INTEGER", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    LastLoginAt = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.UserId);
                    table.ForeignKey(
                        name: "FK_Users_Ranks_RankId",
                        column: x => x.RankId,
                        principalTable: "Ranks",
                        principalColumn: "RankId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "UserRoles",
                columns: table => new
                {
                    UserRoleId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    UserId = table.Column<int>(type: "INTEGER", nullable: false),
                    RoleId = table.Column<int>(type: "INTEGER", nullable: false),
                    AssignedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRoles", x => x.UserRoleId);
                    table.ForeignKey(
                        name: "FK_UserRoles_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "RoleId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserRoles_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Airbases",
                columns: new[] { "AirbaseId", "AirbaseName" },
                values: new object[,]
                {
                    { 1, "قيادة سلاح الجو السلطاني العماني" },
                    { 2, "قاعدة غلا وأكاديمية السلطان قابوس الجوية" },
                    { 3, "قاعدة السيب الجوية" },
                    { 4, "قاعدة صلالة الجوية" },
                    { 5, "قاعدة المصنعة الجوية" },
                    { 6, "قاعدة مصيرة الجوية" },
                    { 7, "قاعدة أدم الجوية" },
                    { 8, "قاعدة ثمريت الجوية" },
                    { 9, "قاعدة خصب الجوية" }
                });

            migrationBuilder.InsertData(
                table: "Categories",
                columns: new[] { "CategoryId", "CategoryCode", "CategoryName", "CreatedAt", "Description", "IsActive", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, "CAT-PLT", "المرشحيين الطيارين", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "فئة المرشحين للالتحاق بسلاح الجو السلطاني العماني كمرشحين طيارين.", true, null },
                    { 2, "CAT-MUG", "المرشحيين الجامعيين العسكريين", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط عسكريين جامعيين", true, null },
                    { 3, "CAT-CUG", "المرشحيين الجامعيين المدنيين", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "خريجوا الكليات والجامعات من حملة البكالوريوس ويتم تقيمهم بناءا على تخصصاتهم المهنية وينضموا للسلاح كضباط مدنيين جامعيين", true, null },
                    { 4, "CAT-LSO", "ضباط الخدمة المحدودة", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط الصف من ذوي الخدمة المحدودة وسسبق لهم العمل في السلاح وهم برتبة وكيل فأعلى", true, null },
                    { 5, "CAT-NCO", "ضباط الصف ( رقباء / عرفاء)", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط صف برتبة رقيب أو عريف سبق لهم العمل بالسلاح", true, null },
                    { 6, "CAT-TCN", "ضباط الصف الكلية التقنية العسكرية", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط صف خريجوا الكلية العسكرية التقنية", true, null },
                    { 7, "CAT-CNP", "ضباط الصف المدنيين للترفيع", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "ضباط صف مدنيين مرشحين للترقية بالصفة المدنية", true, null }
                });

            migrationBuilder.InsertData(
                table: "Ranks",
                columns: new[] { "RankId", "CreatedAt", "Description", "DisplayOrder", "RankName" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(2551), "Senior Air Officer", 1, "Air Marshal" },
                    { 2, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3225), "Senior Air Officer", 2, "Air Vice Marshal" },
                    { 3, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3227), "Senior Air Officer", 3, "Air Commodore" },
                    { 4, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3228), "Senior Officer", 4, "Group Captain" },
                    { 5, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3239), "Senior Officer", 5, "Wing Commander" },
                    { 6, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3240), "Officer", 6, "Squadron Leader" },
                    { 7, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3241), "Officer", 7, "Flight Lieutenant" },
                    { 8, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3241), "Junior Officer", 8, "Flying Officer" },
                    { 9, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3242), "Junior Officer", 9, "Pilot Officer" },
                    { 10, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3243), "Senior NCO", 10, "Warrant Officer" },
                    { 11, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3244), "NCO", 11, "Flight Sergeant" },
                    { 12, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3245), "NCO", 12, "Sergeant" },
                    { 13, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3246), "Junior NCO", 13, "Corporal" },
                    { 14, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3246), "Enlisted", 14, "Senior Aircraftman" },
                    { 15, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3247), "Enlisted", 15, "Leading Aircraftman" },
                    { 16, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(3248), "Enlisted", 16, "Aircraftman" }
                });

            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "RoleId", "CreatedAt", "Description", "RoleName" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7443), "System Administrator with full access", "Admin" },
                    { 2, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7942), "Evaluation Coordinator", "Coordinator" },
                    { 3, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7943), "Personnel Evaluator", "Evaluator" },
                    { 4, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7944), "Read-only access to evaluations", "Viewer" },
                    { 5, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7945), "Human Resources Manager", "HR Manager" },
                    { 6, new DateTime(2025, 7, 9, 4, 1, 14, 344, DateTimeKind.Utc).AddTicks(7946), "Training and Development Officer", "Training Officer" }
                });

            migrationBuilder.InsertData(
                table: "Users",
                columns: new[] { "UserId", "CreatedAt", "IsActive", "LastLoginAt", "Password", "RankId", "ServiceNumber" },
                values: new object[] { 1, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), true, null, "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", 3, "RAFO001" });

            migrationBuilder.InsertData(
                table: "UserRoles",
                columns: new[] { "UserRoleId", "AssignedAt", "RoleId", "UserId" },
                values: new object[] { 1, new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), 1, 1 });

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_AirbaseId",
                table: "Candidates",
                column: "AirbaseId");

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_CategoryId",
                table: "Candidates",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_FullName",
                table: "Candidates",
                column: "FullName");

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_NationalIdNumber",
                table: "Candidates",
                column: "NationalIdNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_RankId",
                table: "Candidates",
                column: "RankId");

            migrationBuilder.CreateIndex(
                name: "IX_Candidates_ServiceNumber",
                table: "Candidates",
                column: "ServiceNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Ranks_RankName",
                table: "Ranks",
                column: "RankName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Roles_RoleName",
                table: "Roles",
                column: "RoleName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_RoleId",
                table: "UserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_UserId_RoleId",
                table: "UserRoles",
                columns: new[] { "UserId", "RoleId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_RankId",
                table: "Users",
                column: "RankId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_ServiceNumber",
                table: "Users",
                column: "ServiceNumber",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Candidates");

            migrationBuilder.DropTable(
                name: "UserRoles");

            migrationBuilder.DropTable(
                name: "Airbases");

            migrationBuilder.DropTable(
                name: "Categories");

            migrationBuilder.DropTable(
                name: "Roles");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropTable(
                name: "Ranks");
        }
    }
}
