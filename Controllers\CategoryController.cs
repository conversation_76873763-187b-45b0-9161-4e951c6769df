using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models;
using RafoEvaluation.ViewModels;
using System.Linq;
using System.Threading.Tasks;

namespace RafoEvaluation.Controllers
{
    public class CategoryController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CategoryController> _logger;

        public CategoryController(ApplicationDbContext context, ILogger<CategoryController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: Category
        public async Task<IActionResult> Index()
        {
            try
            {
                // جلب جميع الفئات - DataTable سيتولى البحث والتصفية والتنقل
                var categories = await _context.Categories
                    .OrderBy(c => c.CategoryName)
                    .Select(c => new CategoryViewModel
                    {
                        CategoryId = c.CategoryId,
                        CategoryName = c.CategoryName,
                        Description = c.Description,
                        CategoryCode = c.CategoryCode,
                        IsActive = c.IsActive,
                        CreatedAt = c.CreatedAt,
                        UpdatedAt = c.UpdatedAt
                    })
                    .ToListAsync();

                // استخدام ViewBag لتمرير المعلومات البسيطة
                ViewBag.TotalCount = categories.Count;
                ViewBag.ActiveCount = categories.Count(c => c.IsActive);
                ViewBag.InactiveCount = categories.Count(c => !c.IsActive);

                return View(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الفئات: {Error}", ex.Message);
                TempData["Error"] = "حدث خطأ أثناء جلب الفئات.";
                return View(new List<CategoryViewModel>());
            }
        }

        // GET: Category/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            try
            {
                var category = await _context.Categories
                    .Where(c => c.CategoryId == id)
                    .Select(c => new CategoryViewModel
                    {
                        CategoryId = c.CategoryId,
                        CategoryName = c.CategoryName,
                        Description = c.Description,
                        CategoryCode = c.CategoryCode,
                        IsActive = c.IsActive,
                        CreatedAt = c.CreatedAt,
                        UpdatedAt = c.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (category == null)
                {
                    return NotFound();
                }

                return View(category);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while retrieving category details for ID: {CategoryId}", id);
                TempData["Error"] = "An error occurred while retrieving category details.";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Category/Create
        public IActionResult Create()
        {
            return View(new CategoryCreateViewModel());
        }

        // POST: Category/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CategoryCreateViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Check if category code already exists
                    var existingCategory = await _context.Categories
                        .AnyAsync(c => c.CategoryCode == model.CategoryCode);

                    if (existingCategory)
                    {
                        ModelState.AddModelError("CategoryCode", "A category with this code already exists.");
                        return View(model);
                    }

                    var category = new Category
                    {
                        CategoryName = model.CategoryName,
                        Description = model.Description,
                        CategoryCode = model.CategoryCode,
                        IsActive = model.IsActive,
                        CreatedAt = DateTime.UtcNow
                    };

                    _context.Categories.Add(category);
                    await _context.SaveChangesAsync();

                    TempData["Success"] = "Category created successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while creating category");
                    ModelState.AddModelError("", "An error occurred while creating the category.");
                }
            }

            return View(model);
        }

        // GET: Category/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            try
            {
                var category = await _context.Categories.FindAsync(id);
                if (category == null)
                {
                    return NotFound();
                }

                var model = new CategoryEditViewModel
                {
                    CategoryId = category.CategoryId,
                    CategoryName = category.CategoryName,
                    Description = category.Description,
                    CategoryCode = category.CategoryCode,
                    IsActive = category.IsActive
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while retrieving category for edit. ID: {CategoryId}", id);
                TempData["Error"] = "An error occurred while retrieving category for editing.";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Category/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CategoryEditViewModel model)
        {
            if (id != model.CategoryId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check if category code already exists for other categories
                    var existingCategory = await _context.Categories
                        .AnyAsync(c => c.CategoryCode == model.CategoryCode && c.CategoryId != model.CategoryId);

                    if (existingCategory)
                    {
                        ModelState.AddModelError("CategoryCode", "A category with this code already exists.");
                        return View(model);
                    }

                    var category = await _context.Categories.FindAsync(id);
                    if (category == null)
                    {
                        return NotFound();
                    }

                    category.CategoryName = model.CategoryName;
                    category.Description = model.Description;
                    category.CategoryCode = model.CategoryCode;
                    category.IsActive = model.IsActive;
                    category.UpdatedAt = DateTime.UtcNow;

                    _context.Update(category);
                    await _context.SaveChangesAsync();

                    TempData["Success"] = "Category updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    if (!CategoryExists(model.CategoryId))
                    {
                        return NotFound();
                    }
                    _logger.LogError(ex, "Concurrency error occurred while updating category. ID: {CategoryId}", id);
                    ModelState.AddModelError("", "The category was modified by another user. Please refresh and try again.");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while updating category. ID: {CategoryId}", id);
                    ModelState.AddModelError("", "An error occurred while updating the category.");
                }
            }

            return View(model);
        }

        // GET: Category/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            try
            {
                var category = await _context.Categories
                    .Where(c => c.CategoryId == id)
                    .Select(c => new CategoryViewModel
                    {
                        CategoryId = c.CategoryId,
                        CategoryName = c.CategoryName,
                        Description = c.Description,
                        CategoryCode = c.CategoryCode,
                        IsActive = c.IsActive,
                        CreatedAt = c.CreatedAt,
                        UpdatedAt = c.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (category == null)
                {
                    return NotFound();
                }

                return View(category);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while retrieving category for deletion. ID: {CategoryId}", id);
                TempData["Error"] = "An error occurred while retrieving category for deletion.";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Category/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var category = await _context.Categories.FindAsync(id);
                if (category == null)
                {
                    return NotFound();
                }

                _context.Categories.Remove(category);
                await _context.SaveChangesAsync();

                TempData["Success"] = "Category deleted successfully.";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting category. ID: {CategoryId}", id);
                TempData["Error"] = "An error occurred while deleting the category. It may be referenced by other records.";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Category/ToggleStatus/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ToggleStatus(int id)
        {
            try
            {
                var category = await _context.Categories.FindAsync(id);
                if (category == null)
                {
                    return Json(new { success = false, message = "Category not found." });
                }

                category.IsActive = !category.IsActive;
                category.UpdatedAt = DateTime.UtcNow;

                _context.Update(category);
                await _context.SaveChangesAsync();

                return Json(new { 
                    success = true, 
                    message = $"Category {(category.IsActive ? "activated" : "deactivated")} successfully.",
                    isActive = category.IsActive 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while toggling category status. ID: {CategoryId}", id);
                return Json(new { success = false, message = "An error occurred while updating the category status." });
            }
        }

        private bool CategoryExists(int id)
        {
            return _context.Categories.Any(e => e.CategoryId == id);
        }
    }
}
