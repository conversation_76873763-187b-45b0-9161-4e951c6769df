using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RafoEvaluation.Data;
using RafoEvaluation.Models;
using RafoEvaluation.ViewModels;
using System.ComponentModel.DataAnnotations;

namespace RafoEvaluation.Controllers
{
    /// <summary>
    /// Controller لإدارة الفئات - Category Management Controller
    /// يتبع أفضل الممارسات في ASP.NET Core MVC
    /// </summary>
    public class CategoryController : Controller
    {
        #region Fields & Constructor

        private readonly ApplicationDbContext _context;
        private readonly ILogger<CategoryController> _logger;

        public CategoryController(ApplicationDbContext context, ILogger<CategoryController> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #endregion

        #region Index - عرض قائمة الفئات

        /// <summary>
        /// عرض قائمة الفئات مع إحصائيات
        /// GET: /Category
        /// </summary>
        /// <returns>صفحة قائمة الفئات</returns>
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                _logger.LogInformation("بدء جلب قائمة الفئات");

                // جلب جميع الفئات مع الترتيب
                var categories = await GetCategoriesAsync();

                // حساب الإحصائيات
                var stats = CalculateStatistics(categories);
                SetViewBagStatistics(stats);

                _logger.LogInformation("تم جلب {Count} فئة بنجاح", categories.Count);
                return View(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب قائمة الفئات");
                TempData["ErrorMessage"] = "حدث خطأ أثناء جلب الفئات. يرجى المحاولة مرة أخرى.";
                return View(new List<CategoryViewModel>());
            }
        }

        #endregion

        #region Create - إنشاء فئة جديدة

        /// <summary>
        /// عرض نموذج إنشاء فئة جديدة
        /// GET: /Category/Create
        /// </summary>
        [HttpGet]
        public IActionResult Create()
        {
            _logger.LogInformation("عرض نموذج إنشاء فئة جديدة");
            return View(new CategoryCreateViewModel());
        }

        /// <summary>
        /// معالجة إنشاء فئة جديدة
        /// POST: /Category/Create
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CategoryCreateViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("نموذج إنشاء الفئة غير صالح");
                    return View(model);
                }

                // التحقق من عدم تكرار الرمز
                if (await IsCategoryCodeExistsAsync(model.CategoryCode))
                {
                    ModelState.AddModelError(nameof(model.CategoryCode), "رمز الفئة موجود مسبقاً");
                    return View(model);
                }

                // إنشاء الفئة الجديدة
                var category = CreateCategoryFromViewModel(model);
                
                _context.Categories.Add(category);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء فئة جديدة: {CategoryName} برمز {CategoryCode}", 
                    category.CategoryName, category.CategoryCode);

                TempData["SuccessMessage"] = $"تم إنشاء الفئة '{category.CategoryName}' بنجاح";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء فئة جديدة");
                TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء الفئة. يرجى المحاولة مرة أخرى.";
                return View(model);
            }
        }

        #endregion

        #region Edit - تعديل فئة

        /// <summary>
        /// عرض نموذج تعديل فئة
        /// GET: /Category/Edit/5
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Edit(int? id)
        {
            if (!id.HasValue)
            {
                _logger.LogWarning("محاولة تعديل فئة بدون معرف");
                return NotFound();
            }

            try
            {
                var category = await _context.Categories.FindAsync(id.Value);
                if (category == null)
                {
                    _logger.LogWarning("لم يتم العثور على الفئة بالمعرف: {Id}", id.Value);
                    return NotFound();
                }

                var viewModel = CreateEditViewModelFromCategory(category);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب بيانات الفئة للتعديل: {Id}", id.Value);
                TempData["ErrorMessage"] = "حدث خطأ أثناء جلب بيانات الفئة.";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// معالجة تعديل فئة
        /// POST: /Category/Edit/5
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CategoryEditViewModel model)
        {
            if (id != model.CategoryId)
            {
                _logger.LogWarning("عدم تطابق معرف الفئة في التعديل");
                return NotFound();
            }

            try
            {
                if (!ModelState.IsValid)
                {
                    return View(model);
                }

                // التحقق من عدم تكرار الرمز (باستثناء الفئة الحالية)
                if (await IsCategoryCodeExistsAsync(model.CategoryCode, id))
                {
                    ModelState.AddModelError(nameof(model.CategoryCode), "رمز الفئة موجود مسبقاً");
                    return View(model);
                }

                var category = await _context.Categories.FindAsync(id);
                if (category == null)
                {
                    return NotFound();
                }

                // تحديث بيانات الفئة
                UpdateCategoryFromViewModel(category, model);
                
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث الفئة: {CategoryName}", category.CategoryName);
                TempData["SuccessMessage"] = $"تم تحديث الفئة '{category.CategoryName}' بنجاح";
                
                return RedirectToAction(nameof(Index));
            }
            catch (DbUpdateConcurrencyException ex)
            {
                _logger.LogError(ex, "تضارب في تحديث الفئة: {Id}", id);
                TempData["ErrorMessage"] = "تم تعديل هذه الفئة من قبل مستخدم آخر. يرجى المحاولة مرة أخرى.";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الفئة: {Id}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث الفئة.";
                return View(model);
            }
        }

        #endregion

        #region Details - عرض تفاصيل فئة

        /// <summary>
        /// عرض تفاصيل فئة
        /// GET: /Category/Details/5
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Details(int? id)
        {
            if (!id.HasValue)
            {
                return NotFound();
            }

            try
            {
                var category = await _context.Categories
                    .AsNoTracking()
                    .FirstOrDefaultAsync(c => c.CategoryId == id.Value);

                if (category == null)
                {
                    return NotFound();
                }

                var viewModel = CreateViewModelFromCategory(category);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب تفاصيل الفئة: {Id}", id.Value);
                TempData["ErrorMessage"] = "حدث خطأ أثناء جلب تفاصيل الفئة.";
                return RedirectToAction(nameof(Index));
            }
        }

        #endregion

        #region Delete - حذف فئة

        /// <summary>
        /// عرض صفحة تأكيد حذف فئة
        /// GET: /Category/Delete/5
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Delete(int? id)
        {
            if (!id.HasValue)
            {
                return NotFound();
            }

            try
            {
                var category = await _context.Categories
                    .AsNoTracking()
                    .FirstOrDefaultAsync(c => c.CategoryId == id.Value);

                if (category == null)
                {
                    return NotFound();
                }

                var viewModel = CreateViewModelFromCategory(category);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب بيانات الفئة للحذف: {Id}", id.Value);
                TempData["ErrorMessage"] = "حدث خطأ أثناء جلب بيانات الفئة.";
                return RedirectToAction(nameof(Index));
            }
        }

        /// <summary>
        /// معالجة حذف فئة
        /// POST: /Category/Delete/5
        /// </summary>
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var category = await _context.Categories.FindAsync(id);
                if (category == null)
                {
                    return NotFound();
                }

                // التحقق من وجود مرشحين مرتبطين بهذه الفئة
                var hasRelatedCandidates = await _context.Candidates
                    .AnyAsync(c => c.CategoryId == id);

                if (hasRelatedCandidates)
                {
                    TempData["ErrorMessage"] = "لا يمكن حذف هذه الفئة لأنها مرتبطة بمرشحين موجودين.";
                    return RedirectToAction(nameof(Index));
                }

                _context.Categories.Remove(category);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم حذف الفئة: {CategoryName}", category.CategoryName);
                TempData["SuccessMessage"] = $"تم حذف الفئة '{category.CategoryName}' بنجاح";

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الفئة: {Id}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف الفئة.";
                return RedirectToAction(nameof(Index));
            }
        }

        #endregion

        #region Toggle Status - تبديل حالة الفئة

        /// <summary>
        /// تبديل حالة الفئة (نشط/غير نشط)
        /// POST: /Category/ToggleStatus/5
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ToggleStatus(int id)
        {
            try
            {
                var category = await _context.Categories.FindAsync(id);
                if (category == null)
                {
                    return Json(new { success = false, message = "لم يتم العثور على الفئة" });
                }

                category.IsActive = !category.IsActive;
                category.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var statusText = category.IsActive ? "نشطة" : "غير نشطة";
                _logger.LogInformation("تم تبديل حالة الفئة {CategoryName} إلى {Status}",
                    category.CategoryName, statusText);

                return Json(new {
                    success = true,
                    message = $"تم تحديث حالة الفئة '{category.CategoryName}' إلى {statusText}"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تبديل حالة الفئة: {Id}", id);
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث حالة الفئة" });
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// جلب جميع الفئات مع الترتيب
        /// </summary>
        private async Task<List<CategoryViewModel>> GetCategoriesAsync()
        {
            return await _context.Categories
                .AsNoTracking()
                .OrderBy(c => c.CategoryName)
                .Select(c => new CategoryViewModel
                {
                    CategoryId = c.CategoryId,
                    CategoryName = c.CategoryName,
                    Description = c.Description,
                    CategoryCode = c.CategoryCode,
                    IsActive = c.IsActive,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt
                })
                .ToListAsync();
        }

        /// <summary>
        /// حساب إحصائيات الفئات
        /// </summary>
        private static (int Total, int Active, int Inactive) CalculateStatistics(List<CategoryViewModel> categories)
        {
            return (
                Total: categories.Count,
                Active: categories.Count(c => c.IsActive),
                Inactive: categories.Count(c => !c.IsActive)
            );
        }

        /// <summary>
        /// تعيين إحصائيات ViewBag
        /// </summary>
        private void SetViewBagStatistics((int Total, int Active, int Inactive) stats)
        {
            ViewBag.TotalCount = stats.Total;
            ViewBag.ActiveCount = stats.Active;
            ViewBag.InactiveCount = stats.Inactive;
        }

        /// <summary>
        /// التحقق من وجود رمز الفئة
        /// </summary>
        private async Task<bool> IsCategoryCodeExistsAsync(string categoryCode, int? excludeId = null)
        {
            var query = _context.Categories.Where(c => c.CategoryCode == categoryCode);

            if (excludeId.HasValue)
            {
                query = query.Where(c => c.CategoryId != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// إنشاء كائن Category من ViewModel
        /// </summary>
        private static Category CreateCategoryFromViewModel(CategoryCreateViewModel model)
        {
            return new Category
            {
                CategoryName = model.CategoryName.Trim(),
                Description = string.IsNullOrWhiteSpace(model.Description) ? null : model.Description.Trim(),
                CategoryCode = model.CategoryCode.Trim().ToUpper(),
                IsActive = model.IsActive,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// إنشاء EditViewModel من Category
        /// </summary>
        private static CategoryEditViewModel CreateEditViewModelFromCategory(Category category)
        {
            return new CategoryEditViewModel
            {
                CategoryId = category.CategoryId,
                CategoryName = category.CategoryName,
                Description = category.Description,
                CategoryCode = category.CategoryCode,
                IsActive = category.IsActive
            };
        }

        /// <summary>
        /// تحديث Category من EditViewModel
        /// </summary>
        private static void UpdateCategoryFromViewModel(Category category, CategoryEditViewModel model)
        {
            category.CategoryName = model.CategoryName.Trim();
            category.Description = string.IsNullOrWhiteSpace(model.Description) ? null : model.Description.Trim();
            category.CategoryCode = model.CategoryCode.Trim().ToUpper();
            category.IsActive = model.IsActive;
            category.UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// إنشاء ViewModel من Category
        /// </summary>
        private static CategoryViewModel CreateViewModelFromCategory(Category category)
        {
            return new CategoryViewModel
            {
                CategoryId = category.CategoryId,
                CategoryName = category.CategoryName,
                Description = category.Description,
                CategoryCode = category.CategoryCode,
                IsActive = category.IsActive,
                CreatedAt = category.CreatedAt,
                UpdatedAt = category.UpdatedAt
            };
        }

        #endregion

        #region Dispose

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
