@model RafoEvaluation.ViewModels.CandidateViewModel
@{
    ViewData["Title"] = "Delete Candidate";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-trash me-2"></i>Delete Candidate
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning!</strong> You are about to permanently delete this candidate. This action cannot be undone.
                    </div>

                    <h5 class="mb-3">Are you sure you want to delete this candidate?</h5>
                    
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Full Name:</label>
                                <p class="form-control-plaintext">@Model.FullName</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Service Number:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-secondary">@Model.ServiceNumber</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">National ID Number:</label>
                                <p class="form-control-plaintext">@Model.NationalIdNumber</p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Age:</label>
                                <p class="form-control-plaintext">@Model.Age years old</p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Category:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-info">@Model.CategoryName</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Rank:</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-warning text-dark">@Model.RankName</span>
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Department:</label>
                                <p class="form-control-plaintext">
                                    @if (!string.IsNullOrEmpty(Model.Department))
                                    {
                                        @Model.Department
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">Not specified</span>
                                    }
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Status:</label>
                                <p class="form-control-plaintext">
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">Active</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">Inactive</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Education Information (if available) -->
                    @if (!string.IsNullOrEmpty(Model.University) || !string.IsNullOrEmpty(Model.Major) || Model.GraduationYear.HasValue)
                    {
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6 class="text-primary">Education Information:</h6>
                            </div>
                            
                            @if (!string.IsNullOrEmpty(Model.University))
                            {
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">University:</label>
                                        <p class="form-control-plaintext">@Model.University</p>
                                    </div>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(Model.Major))
                            {
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Major:</label>
                                        <p class="form-control-plaintext">@Model.Major</p>
                                    </div>
                                </div>
                            }

                            @if (Model.GraduationYear.HasValue)
                            {
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Graduation Year:</label>
                                        <p class="form-control-plaintext">@Model.GraduationYear.Value</p>
                                    </div>
                                </div>
                            }
                        </div>
                    }

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end">
                                <a asp-action="Index" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </a>
                                <form asp-action="Delete" method="post" class="d-inline">
                                    <input type="hidden" asp-for="CandidateId" />
                                    <button type="submit" class="btn btn-danger" 
                                            onclick="return confirm('Are you absolutely sure you want to delete this candidate? This action cannot be undone.')">
                                        <i class="fas fa-trash me-1"></i>Delete Permanently
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
