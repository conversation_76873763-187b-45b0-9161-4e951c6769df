@model RafoEvaluation.ViewModels.CandidateListViewModel
@{
    ViewData["Title"] = "Candidates";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>Candidates Management
                    </h3>
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>Add New Candidate
                    </a>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" name="searchTerm" value="@Model.SearchTerm" 
                                           class="form-control" placeholder="Search by name, service number, ID...">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select name="categoryFilter" class="form-select">
                                    <option value="0">All Categories</option>
                                    @foreach (var category in Model.Categories)
                                    {
                                        <option value="@category.CategoryId" 
                                                selected="@(category.CategoryId == Model.CategoryFilter)">
                                            @category.CategoryName
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="rankFilter" class="form-select">
                                    <option value="0">All Ranks</option>
                                    @foreach (var rank in Model.Ranks)
                                    {
                                        <option value="@rank.RankId" 
                                                selected="@(rank.RankId == Model.RankFilter)">
                                            @rank.RankName
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-2">
                                @if (!string.IsNullOrEmpty(Model.SearchTerm) || Model.CategoryFilter > 0 || Model.RankFilter > 0)
                                {
                                    <a asp-action="Index" class="btn btn-secondary w-100">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </a>
                                }
                            </div>
                        </div>
                    </form>

                    <!-- Results Info -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="text-muted mb-0">
                                Showing @((Model.CurrentPage - 1) * Model.PageSize + 1) to 
                                @(Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCount)) of 
                                @Model.TotalCount entries
                                @if (!string.IsNullOrEmpty(Model.SearchTerm) || Model.CategoryFilter > 0 || Model.RankFilter > 0)
                                {
                                    <span>(filtered from total entries)</span>
                                }
                            </p>
                        </div>
                    </div>

                    <!-- Candidates Table -->
                    @if (Model.Candidates.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Service Number</th>
                                        <th>Full Name</th>
                                        <th>Category</th>
                                        <th>Rank</th>
                                        <th>Age</th>
                                        <th>Department</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.Candidates)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">@item.ServiceNumber</span>
                                            </td>
                                            <td>
                                                <strong>@item.FullName</strong>
                                                <br>
                                                <small class="text-muted">ID: @item.NationalIdNumber</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@item.CategoryName</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark">@item.RankName</span>
                                            </td>
                                            <td>
                                                @item.Age years
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(item.Department))
                                                {
                                                    <span>@item.Department</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted fst-italic">Not specified</span>
                                                }
                                            </td>
                                            <td>
                                                @if (item.IsActive)
                                                {
                                                    <span class="badge bg-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.CandidateId" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.CandidateId" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="toggleStatus(@item.CandidateId, '@item.FullName')" 
                                                            title="Toggle Status">
                                                        <i class="fas fa-toggle-@(item.IsActive ? "on" : "off")"></i>
                                                    </button>
                                                    <a asp-action="Delete" asp-route-id="@item.CandidateId" 
                                                       class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (Model.TotalPages > 1)
                        {
                            <nav aria-label="Candidates pagination">
                                <ul class="pagination justify-content-center">
                                    <!-- Previous -->
                                    <li class="page-item @(Model.CurrentPage == 1 ? "disabled" : "")">
                                        <a class="page-link" asp-action="Index" 
                                           asp-route-page="@(Model.CurrentPage - 1)" 
                                           asp-route-searchTerm="@Model.SearchTerm"
                                           asp-route-categoryFilter="@Model.CategoryFilter"
                                           asp-route-rankFilter="@Model.RankFilter">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </a>
                                    </li>

                                    <!-- Page Numbers -->
                                    @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                            <a class="page-link" asp-action="Index" 
                                               asp-route-page="@i" 
                                               asp-route-searchTerm="@Model.SearchTerm"
                                               asp-route-categoryFilter="@Model.CategoryFilter"
                                               asp-route-rankFilter="@Model.RankFilter">
                                                @i
                                            </a>
                                        </li>
                                    }

                                    <!-- Next -->
                                    <li class="page-item @(Model.CurrentPage == Model.TotalPages ? "disabled" : "")">
                                        <a class="page-link" asp-action="Index" 
                                           asp-route-page="@(Model.CurrentPage + 1)" 
                                           asp-route-searchTerm="@Model.SearchTerm"
                                           asp-route-categoryFilter="@Model.CategoryFilter"
                                           asp-route-rankFilter="@Model.RankFilter">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No candidates found</h4>
                            @if (!string.IsNullOrEmpty(Model.SearchTerm) || Model.CategoryFilter > 0 || Model.RankFilter > 0)
                            {
                                <p class="text-muted">
                                    No candidates match your search criteria. 
                                    <a asp-action="Index" class="text-decoration-none">Clear filters</a> to see all candidates.
                                </p>
                            }
                            else
                            {
                                <p class="text-muted">
                                    Get started by <a asp-action="Create" class="text-decoration-none">adding your first candidate</a>.
                                </p>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- JavaScript for Toggle Status -->
    <script>
        function toggleStatus(candidateId, candidateName) {
            if (confirm(`Are you sure you want to toggle the status of "${candidateName}"?`)) {
                fetch(`/Candidate/ToggleStatus/${candidateId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'An error occurred while updating the status.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating the status.');
                });
            }
        }
    </script>
}
