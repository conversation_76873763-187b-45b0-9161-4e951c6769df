@model RafoEvaluation.ViewModels.CandidateListViewModel
@{
    ViewData["Title"] = "المرشحين";
}

<!-- Breadcrumb -->
<div class="breadcrumb-container">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="page-title">
                    <i class="fas fa-users breadcrumb-icon"></i>
                    إدارة المرشحين
                </h1>
            </div>
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-custom justify-content-md-end mb-0">
                        <li class="breadcrumb-item">
                            <a asp-action="Index" asp-controller="Home">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-users"></i> المرشحين
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-users ms-2"></i>إدارة المرشحين
                    </h3>
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus ms-1"></i>إضافة مرشح جديد
                    </a>
                </div>
                <div class="card-body">
                    <!-- نموذج البحث والتصفية -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" name="searchTerm" value="@Model.SearchTerm"
                                           class="form-control" placeholder="البحث بالاسم، الرقم العسكري، الهوية...">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select name="categoryFilter" class="form-select">
                                    <option value="0">جميع الفئات</option>
                                    @foreach (var category in Model.Categories)
                                    {
                                        <option value="@category.CategoryId"
                                                selected="@(category.CategoryId == Model.CategoryFilter)">
                                            @category.CategoryName
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="rankFilter" class="form-select">
                                    <option value="0">جميع الرتب</option>
                                    @foreach (var rank in Model.Ranks)
                                    {
                                        <option value="@rank.RankId"
                                                selected="@(rank.RankId == Model.RankFilter)">
                                            @rank.RankName
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-2">
                                @if (!string.IsNullOrEmpty(Model.SearchTerm) || Model.CategoryFilter > 0 || Model.RankFilter > 0)
                                {
                                    <a asp-action="Index" class="btn btn-secondary w-100">
                                        <i class="fas fa-times ms-1"></i>مسح
                                    </a>
                                }
                            </div>
                        </div>
                    </form>

                    <!-- معلومات النتائج -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="text-muted mb-0">
                                عرض @((Model.CurrentPage - 1) * Model.PageSize + 1) إلى
                                @(Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCount)) من
                                @Model.TotalCount إدخال
                                @if (!string.IsNullOrEmpty(Model.SearchTerm) || Model.CategoryFilter > 0 || Model.RankFilter > 0)
                                {
                                    <span>(مفلتر من إجمالي الإدخالات)</span>
                                }
                            </p>
                        </div>
                    </div>

                    <!-- جدول المرشحين -->
                    @if (Model.Candidates.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الرقم العسكري</th>
                                        <th>الاسم الكامل</th>
                                        <th>الفئة</th>
                                        <th>الرتبة</th>
                                        <th>العمر</th>
                                        <th>القسم</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.Candidates)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">@item.ServiceNumber</span>
                                            </td>
                                            <td>
                                                <strong>@item.FullName</strong>
                                                <br>
                                                <small class="text-muted">ID: @item.NationalIdNumber</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@item.CategoryName</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark">@item.RankName</span>
                                            </td>
                                            <td>
                                                @item.Age سنة
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(item.Department))
                                                {
                                                    <span>@item.Department</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted fst-italic">غير محدد</span>
                                                }
                                            </td>
                                            <td>
                                                @if (item.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.CandidateId"
                                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.CandidateId"
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                                            onclick="toggleStatus(@item.CandidateId, '@item.FullName')"
                                                            title="تبديل الحالة">
                                                        <i class="fas fa-toggle-@(item.IsActive ? "on" : "off")"></i>
                                                    </button>
                                                    <a asp-action="Delete" asp-route-id="@item.CandidateId"
                                                       class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- التنقل بين الصفحات -->
                        @if (Model.TotalPages > 1)
                        {
                            <nav aria-label="تنقل المرشحين">
                                <ul class="pagination justify-content-center">
                                    <!-- السابق -->
                                    <li class="page-item @(Model.CurrentPage == 1 ? "disabled" : "")">
                                        <a class="page-link" asp-action="Index"
                                           asp-route-page="@(Model.CurrentPage - 1)"
                                           asp-route-searchTerm="@Model.SearchTerm"
                                           asp-route-categoryFilter="@Model.CategoryFilter"
                                           asp-route-rankFilter="@Model.RankFilter">
                                            <i class="fas fa-chevron-right"></i> السابق
                                        </a>
                                    </li>

                                    <!-- Page Numbers -->
                                    @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                            <a class="page-link" asp-action="Index" 
                                               asp-route-page="@i" 
                                               asp-route-searchTerm="@Model.SearchTerm"
                                               asp-route-categoryFilter="@Model.CategoryFilter"
                                               asp-route-rankFilter="@Model.RankFilter">
                                                @i
                                            </a>
                                        </li>
                                    }

                                    <!-- التالي -->
                                    <li class="page-item @(Model.CurrentPage == Model.TotalPages ? "disabled" : "")">
                                        <a class="page-link" asp-action="Index"
                                           asp-route-page="@(Model.CurrentPage + 1)"
                                           asp-route-searchTerm="@Model.SearchTerm"
                                           asp-route-categoryFilter="@Model.CategoryFilter"
                                           asp-route-rankFilter="@Model.RankFilter">
                                            التالي <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لم يتم العثور على مرشحين</h4>
                            @if (!string.IsNullOrEmpty(Model.SearchTerm) || Model.CategoryFilter > 0 || Model.RankFilter > 0)
                            {
                                <p class="text-muted">
                                    لا يوجد مرشحين يطابقون معايير البحث الخاصة بك.
                                    <a asp-action="Index" class="text-decoration-none">مسح المرشحات</a> لرؤية جميع المرشحين.
                                </p>
                            }
                            else
                            {
                                <p class="text-muted">
                                    ابدأ بـ <a asp-action="Create" class="text-decoration-none">إضافة أول مرشح</a>.
                                </p>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- JavaScript for Toggle Status -->
    <script>
        function toggleStatus(candidateId, candidateName) {
            if (confirm(`Are you sure you want to toggle the status of "${candidateName}"?`)) {
                fetch(`/Candidate/ToggleStatus/${candidateId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'An error occurred while updating the status.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating the status.');
                });
            }
        }
    </script>
}
