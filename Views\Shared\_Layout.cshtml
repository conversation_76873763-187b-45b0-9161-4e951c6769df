﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - RafoEvaluation</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/RafoEvaluation.styles.css" asp-append-version="true" />
</head>
<body class="d-flex flex-column min-vh-100">
    <div class="wrapper d-flex">
        <!-- Sidebar -->
        <nav id="sidebar" class="sidebar collapse d-lg-block">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <i class="fas fa-chart-line me-2"></i>
                    <span class="brand-text">RAFO Evaluation</span>
                </div>
            </div>
            <div class="sidebar-content">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="@Url.Action("Index", "Home")">
                            <i class="fas fa-home me-2"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#evaluationsSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                            <i class="fas fa-clipboard-list me-2"></i>
                            <span>Evaluations</span>
                            <i class="fas fa-chevron-down ms-auto"></i>
                        </a>
                        <div class="collapse" id="evaluationsSubmenu">
                            <ul class="nav flex-column ms-3">
                                <li class="nav-item">
                                    <a class="nav-link" href="#">
                                        <i class="fas fa-plus me-2"></i>
                                        <span>New Evaluation</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#">
                                        <i class="fas fa-list me-2"></i>
                                        <span>All Evaluations</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="@Url.Action("Index", "Candidate")">
                            <i class="fas fa-users me-2"></i>
                            <span>Candidates</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="@Url.Action("Index", "Category")">
                            <i class="fas fa-list-alt me-2"></i>
                            <span>Categories</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="@Url.Action("Index", "Airbase")">
                            <i class="fas fa-building me-2"></i>
                            <span>Airbases</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-chart-bar me-2"></i>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-cog me-2"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="@Url.Action("Privacy", "Home")">
                            <i class="fas fa-shield-alt me-2"></i>
                            <span>Privacy</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <div id="content" class="content flex-grow-1">
            <!-- Top Navigation -->
            <header class="topbar">
                <nav class="navbar navbar-expand-lg">
                    <div class="container-fluid">
                        <button class="btn btn-outline-light d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar" aria-controls="sidebar" aria-expanded="false" aria-label="Toggle navigation">
                            <i class="fas fa-bars"></i>
                        </button>
                        <button class="btn btn-outline-light d-none d-lg-block" type="button" id="sidebarToggle">
                            <i class="fas fa-bars"></i>
                        </button>
                        
                        <div class="navbar-nav ms-auto">
                            <!-- Theme Toggle -->
                            <div class="nav-item me-3">
                                <button class="btn btn-outline-light theme-toggle" id="themeToggle" type="button" title="Toggle Theme">
                                    <i class="fas fa-moon theme-icon"></i>
                                </button>
                            </div>
                            
                            <!-- Notifications -->
                            <div class="nav-item dropdown me-3">
                                <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-bell"></i>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge">
                                        3
                                    </span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                                    <li><h6 class="dropdown-header">Notifications</h6></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-info-circle me-2 text-info"></i>New evaluation submitted</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-user-plus me-2 text-success"></i>New candidate registered</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-chart-line me-2 text-warning"></i>Report generated</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-center" href="#">View all notifications</a></li>
                                </ul>
                            </div>

                            <!-- User Menu -->
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="user-avatar me-2">
                                        <i class="fas fa-user-circle"></i>
                                    </div>
                                    <span class="user-name">John Doe</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end user-dropdown">
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>My Profile</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Account Settings</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-palette me-2"></i>Appearance</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-question-circle me-2"></i>Help & Support</a></li>
                                    <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
            </header>

            <!-- Main Content Area -->
            <main class="main-content">
                <div class="container-fluid">
                    @RenderBody()
                </div>
            </main>

            <!-- Footer -->
            <footer class="footer mt-auto">
                <div class="container-fluid">
                    <div class="row align-items-center">
                    
                        <div class="col-md-6 text-md-end text-center mt-2 mt-md-0">
                            <a href="@Url.Action("Privacy", "Home")" class="text-muted text-decoration-none me-3">Privacy Policy</a>
                            <a href="#" class="text-muted text-decoration-none">Terms of Service</a>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
