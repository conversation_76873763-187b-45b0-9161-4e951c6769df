/* ===== CSS VARIABLES FOR THEMING ===== */
/* Updated: 2025-07-09 - Arabic RTL Support */
:root {
    /* Light Theme Colors */
    --primary-color: #161D6F;
    --primary-hover: #0F1456;
    --secondary-color: #1E2A8A;
    --accent-color: #2563EB;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    
    /* Light Theme Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-sidebar: #ffffff;
    --bg-topbar: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --bg-card: #ffffff;
    --bg-input: #ffffff;
    
    /* Light Theme Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-white: #ffffff;
    --text-sidebar: #475569;
    
    /* Light Theme Border Colors */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    --border-focus: #4f46e5;
    
    /* Light Theme Shadow */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Sidebar */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    
    /* Transitions - Disabled */
    --transition-fast: 0s;
    --transition-normal: 0s;
    --transition-slow: 0s;
}

/* Dark Theme Colors */
[data-theme="dark"] {
    /* Dark Theme Background Colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-sidebar: #1e293b;
    --bg-topbar: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --bg-card: #1e293b;
    --bg-input: #334155;
    
    /* Dark Theme Text Colors */
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-sidebar: #cbd5e1;
    
    /* Dark Theme Border Colors */
    --border-color: #334155;
    --border-hover: #475569;
    
    /* Dark Theme Shadow */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* ===== GLOBAL STYLES ===== */
* {
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    direction: rtl !important;
    text-align: right !important;
    font-weight: 400;
    line-height: 1.6;
}

/* Force Arabic font for text elements only, preserve icon fonts */
body, p, h1, h2, h3, h4, h5, h6, span, div, a, button, input, textarea, select, label {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Preserve Font Awesome for icons */
.fas, .far, .fab, .fal, .fad, .fa {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands" !important;
    font-weight: 900 !important;
}

/* Ensure icons display properly */
i[class*="fa-"]::before {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
}

/* Force icon display */
.fas, .far, .fab, .fal, .fad {
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
}

/* Specific icon fixes */
.fa-bars::before { content: "\f0c9" !important; }
.fa-moon::before { content: "\f186" !important; }
.fa-sun::before { content: "\f185" !important; }
.fa-user-circle::before { content: "\f2bd" !important; }
.fa-home::before { content: "\f015" !important; }
.fa-clipboard-list::before { content: "\f46d" !important; }
.fa-users::before { content: "\f0c0" !important; }
.fa-list-alt::before { content: "\f022" !important; }
.fa-building::before { content: "\f1ad" !important; }
.fa-chart-bar::before { content: "\f080" !important; }
.fa-cog::before { content: "\f013" !important; }
.fa-shield-alt::before { content: "\f3ed" !important; }

/* Hide fallback text when icons load properly */
.fas::before, .far::before, .fab::before, .fal::before, .fad::before {
    font-size: inherit !important;
}

/* Show fallback text only when icons fail to load */
.fas:not([class*="fa-"])::before,
.far:not([class*="fa-"])::before,
.fab:not([class*="fa-"])::before {
    content: none !important;
}

/* Ensure proper icon sizing */
.topbar .fas, .topbar .far, .topbar .fab {
    font-size: 1.1rem !important;
    width: auto !important;
    text-align: center !important;
}

/* Sidebar Dropdown Styles */
.sidebar .nav-link[data-bs-toggle="collapse"] {
    position: relative;
}

.sidebar .nav-link[data-bs-toggle="collapse"] .fa-chevron-down {
    font-size: 0.8rem;
    margin-right: auto;
}

.sidebar .nav-link[data-bs-toggle="collapse"]:not(.collapsed) .fa-chevron-down {
    transform: rotate(180deg);
}

.sidebar .collapse .nav {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.sidebar .collapse .nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border-radius: 0.5rem;
    margin: 0.2rem 0;
    background-color: rgba(22, 29, 111, 0.05);
    border-right: 3px solid transparent;
}

.sidebar .collapse .nav-link:hover {
    background-color: rgba(22, 29, 111, 0.1);
    border-right-color: var(--primary-color);
    transform: translateX(-2px);
    color: var(--primary-color);
}

.sidebar .collapse .nav-link.active {
    background-color: rgba(22, 29, 111, 0.15);
    border-right-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 600;
}

/* Breadcrumb Styles */
.breadcrumb-container {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
    padding: 1.5rem 0;
    margin-bottom: 2rem;
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px rgba(22, 29, 111, 0.2);
}

.breadcrumb-custom {
    background: transparent;
    margin-bottom: 0;
    padding: 0;
}

.breadcrumb-custom .breadcrumb-item {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.breadcrumb-custom .breadcrumb-item.active {
    color: #ffffff;
    font-weight: 600;
}

.breadcrumb-custom .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
}

.breadcrumb-custom .breadcrumb-item a:hover {
    color: #ffffff;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

.breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
    content: "❯";
    color: rgba(255, 255, 255, 0.6);
    font-weight: bold;
}

.breadcrumb-icon {
    margin-left: 0.5rem;
    font-size: 1.1rem;
}

.page-title {
    color: #ffffff;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    margin-bottom: 0;
}

/* Notification Styles - أنماط الإشعارات */
.alert.position-fixed {
    border-radius: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: none;
    font-weight: 500;
}

.alert.position-fixed .btn-close {
    margin-left: 0.5rem;
    margin-right: 0;
}

.alert-success {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

body {
    display: flex;
    flex-direction: column;
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .text-left {
    text-align: right !important;
}

[dir="rtl"] .text-right {
    text-align: left !important;
}

/* RTL Sidebar Adjustments */
[dir="rtl"] .sidebar {
    right: 0 !important;
    left: auto !important;
    border-left: 1px solid var(--border-color);
    border-right: none;
}

/* Force sidebar positioning in RTL */
.sidebar {
    position: fixed !important;
    top: 0 !important;
    height: 100vh !important;
    z-index: 1000 !important;
}

[dir="rtl"] .content {
    margin-right: var(--sidebar-width) !important;
    margin-left: 0 !important;
}

[dir="rtl"] .content.expanded {
    margin-right: var(--sidebar-collapsed-width) !important;
    margin-left: 0 !important;
}

/* Ensure content area is properly positioned */
.content {
    /* transition removed */
}

/* RTL Navigation Adjustments */
[dir="rtl"] .nav-link i {
    margin-left: 0.5rem;
    margin-right: 0;
}

[dir="rtl"] .dropdown-menu-end {
    right: auto !important;
    left: 0 !important;
}

[dir="rtl"] .dropdown-menu-start {
    left: auto !important;
    right: 0 !important;
}

/* RTL Topbar Adjustments */
[dir="rtl"] .topbar .navbar-nav {
    flex-direction: row;
}

[dir="rtl"] .topbar .navbar-nav:first-child {
    margin-left: 0;
    margin-right: auto;
}

[dir="rtl"] .topbar .navbar-nav.ms-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

/* Removed notification badge styles as notifications are removed */

[dir="rtl"] .user-avatar {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
}

/* Force RTL layout for topbar */
[dir="rtl"] .topbar .container-fluid {
    flex-direction: row !important;
}

[dir="rtl"] .topbar .navbar-nav {
    flex-direction: row !important;
}

/* Ensure proper spacing in RTL */
[dir="rtl"] .topbar .nav-item {
    margin-left: 0;
    margin-right: 0.75rem;
}

[dir="rtl"] .topbar .nav-item:last-child {
    margin-right: 0;
}

/* RTL Bootstrap Overrides */
[dir="rtl"] .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .me-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .ms-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

[dir="rtl"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

/* Arabic Typography Improvements */
[dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, [dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    line-height: 1.4;
}

[dir="rtl"] .btn {
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

[dir="rtl"] .form-label {
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

[dir="rtl"] .nav-link {
    font-family: 'Cairo', sans-serif;
    font-weight: 400;
}

[dir="rtl"] .sidebar-brand {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
}

/* RTL Input Groups */
[dir="rtl"] .input-group-text {
    border-left: 1px solid var(--border-color);
    border-right: none;
}

[dir="rtl"] .input-group > .form-control:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

[dir="rtl"] .input-group > .form-control:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.min-vh-100 {
    min-height: 100vh !important;
}

/* ===== LAYOUT STRUCTURE ===== */
.wrapper {
    min-height: 100vh;
    flex: 1;
    display: flex;
}

/* ===== SIDEBAR STYLES ===== */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-sidebar);
    border-left: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
    overflow-y: auto;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
    box-shadow: 0 2px 8px rgba(22, 29, 111, 0.2);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    color: var(--text-white);
    font-size: 1.25rem;
    font-weight: 700;
    text-decoration: none;
}

.sidebar-brand i {
    font-size: 1.5rem;
    background: linear-gradient(45deg, #ffffff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar.collapsed .brand-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-content {
    padding: 1rem 0;
}

.sidebar .nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.5rem;
    color: var(--text-sidebar);
    text-decoration: none;
    border: none;
    border-radius: 0;
    position: relative;
    margin: 0.125rem 0.5rem;
    border-radius: 0.5rem;
}

.sidebar .nav-link:hover {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(99, 102, 241, 0.1));
    color: var(--primary-color);
    transform: translateX(4px);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
    color: var(--text-white);
    box-shadow: 0 2px 8px rgba(22, 29, 111, 0.3);
    border: 1px solid rgba(30, 42, 138, 0.4);
    transform: translateX(-2px);
}

.sidebar .nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: var(--text-white);
    border-radius: 0 4px 4px 0;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

.sidebar .nav-link span {
    /* transition removed */
}

.sidebar.collapsed .nav-link span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.875rem;
}

/* Submenu Styles */
.sidebar .nav-link[data-bs-toggle="collapse"]::after {
    content: none;
}

.sidebar .nav-link .fa-chevron-down {
    /* transition removed */
}

.sidebar .nav-link[aria-expanded="true"] .fa-chevron-down {
    transform: rotate(180deg);
}

/* ===== MAIN CONTENT AREA ===== */
.content {
    margin-right: var(--sidebar-width);
    background: var(--bg-secondary);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.content.expanded {
    margin-right: var(--sidebar-collapsed-width);
}

/* ===== TOPBAR STYLES ===== */
.topbar {
    background: var(--bg-topbar);
    box-shadow: 0 2px 8px rgba(22, 29, 111, 0.2);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(30, 42, 138, 0.2);
    border-left: 1px solid rgba(30, 42, 138, 0.2);
    position: sticky;
    top: 0;
    z-index: 999;
}

.topbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    pointer-events: none;
}

.topbar .navbar {
    padding: 1rem 1.5rem;
}

.topbar .navbar-nav .nav-link {
    color: var(--text-white) !important;
}

.topbar .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.3);
    color: var(--text-white);
    background-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 1px 4px rgba(22, 29, 111, 0.1);
}

.topbar .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.5);
    color: var(--text-white);
    /* transform and box-shadow removed */
}

.topbar .user-name {
    color: var(--text-white) !important;
}

.topbar .user-avatar i {
    color: var(--text-white) !important;
}

/* Removed notification badge styles */

.topbar .nav-link i {
    color: var(--text-white) !important;
}

/* Topbar dropdown styling */
.topbar .dropdown-toggle::after {
    border-top-color: var(--text-white) !important;
}

.topbar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 0.5rem;
}

/* Theme toggle button in topbar */
.topbar .theme-toggle {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.topbar .theme-toggle:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

/* Improved topbar layout for RTL */
[dir="rtl"] .topbar .navbar-nav.d-flex.flex-row {
    gap: 0.75rem;
}

[dir="rtl"] .topbar .nav-item {
    display: flex;
    align-items: center;
}

/* Sidebar toggle button styling */
.topbar .btn[id="sidebarToggle"],
.topbar .btn[data-bs-target="#sidebar"] {
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

.topbar .btn[id="sidebarToggle"]:hover,
.topbar .btn[data-bs-target="#sidebar"]:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

/* RTL Topbar Layout */
[dir="rtl"] .topbar .container-fluid {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

[dir="rtl"] .topbar .navbar-nav:first-child {
    order: 1; /* User menu on the left */
}

[dir="rtl"] .topbar .navbar-nav.ms-auto {
    order: 2; /* Notifications and theme toggle in the middle */
}

[dir="rtl"] .topbar .btn.d-none.d-lg-block,
[dir="rtl"] .topbar .btn.d-lg-none {
    order: 3; /* Sidebar toggle on the right */
}

.topbar .navbar-brand {
    font-weight: 700;
    color: var(--text-white);
}

/* Theme Toggle Button */
.theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-white);
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    /* transform and box-shadow removed */
}

.theme-icon {
    /* transition removed */
}

/* Notification Badge */
.notification-badge {
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
}

/* User Avatar */
.user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-color), var(--info-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
}

.user-name {
    font-weight: 500;
    color: var(--text-white);
}

/* Dropdown Menus */
.notification-dropdown,
.user-dropdown {
    border: none;
    box-shadow: var(--shadow-xl);
    border-radius: 0.75rem;
    padding: 0.5rem 0;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    min-width: 250px;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    border-radius: 0.5rem;
    margin: 0 0.5rem;
}

.dropdown-item:hover {
    background: var(--bg-tertiary);
    color: var(--primary-color);
    transform: translateX(4px);
}

.dropdown-header {
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.5rem 1rem 0.25rem;
}

/* ===== MAIN CONTENT STYLES ===== */
.main-content {
    flex: 1 0 auto; /* Allow growth, prevent shrinking, auto basis */
    padding: 2rem;
    background: var(--bg-primary);
    border-radius: 1rem 1rem 0 0;
    margin-top: 1rem;
    box-shadow: var(--shadow-lg);
    min-height: 0; /* Allow content to shrink if needed */
}

/* ===== CARDS ===== */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
}

.card:hover {
    /* hover effects removed */
}

.card-header {
    background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-secondary));
    border-bottom: 1px solid var(--border-color);
    border-radius: 1rem 1rem 0 0 !important;
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* ===== BUTTONS ===== */
.btn {
    border-radius: 0.75rem;
    font-weight: 500;
    border: none;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
    color: var(--text-white);
    box-shadow: 0 2px 8px rgba(22, 29, 111, 0.3);
    border: 1px solid rgba(30, 42, 138, 0.4);
    font-weight: 600;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 50%, var(--secondary-color) 100%);
    /* transform and box-shadow removed */
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
}

.btn-outline-light {
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.9);
    background: transparent;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
    border-color: rgba(255, 255, 255, 0.5);
}

/* ===== FORMS ===== */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 0.875rem 1rem;
    background: var(--bg-input);
    color: var(--text-primary);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
    background: var(--bg-input);
    color: var(--text-primary);
}

.form-label {
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--bg-card);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 0;
    margin-top: auto; /* Push footer to bottom */
    flex-shrink: 0; /* Prevent footer from shrinking */
    width: 100%;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    position: relative; /* Ensure proper positioning */
}

.footer .text-muted {
    color: var(--text-muted) !important;
    font-size: 0.875rem;
}

.footer a {
    /* transition removed */
}

.footer a:hover {
    color: var(--primary-color) !important;
}

/* Footer dark theme specific styles */
[data-theme="dark"] .footer {
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .footer .text-muted {
    color: var(--text-muted) !important;
}

[data-theme="dark"] .footer a:hover {
    color: var(--accent-color) !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 991.98px) {
    .sidebar {
        margin-right: calc(-1 * var(--sidebar-width));
    }

    .sidebar.show {
        margin-right: 0;
    }

    .content {
        margin-right: 0;
    }
    
    .main-content {
        padding: 1rem;
        margin-top: 0.5rem;
    }
    
    .footer {
        padding: 1rem 0;
    }
    
    .footer .row {
        text-align: center;
    }
    
    .footer .col-md-6:last-child {
        text-align: center !important;
        margin-top: 0.5rem;
    }
    
    .topbar .navbar {
        padding: 0.75rem 1rem;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 0.75rem;
        border-radius: 0.5rem 0.5rem 0 0;
    }
    
    .footer {
        padding: 0.75rem 0;
    }
    
    .footer .container-fluid {
        padding: 0 0.75rem;
    }
    
    .card {
        border-radius: 0.75rem;
    }
    
    .sidebar-brand {
        font-size: 1.1rem;
    }
    
    .user-name {
        display: none;
    }
    
    .notification-dropdown,
    .user-dropdown {
        min-width: 200px;
    }
}

/* ===== LOGIN PAGE STYLES ===== */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
    max-width: 420px;
    width: 100%;
    margin: 20px;
    color: var(--text-primary);
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h2 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

.login-header p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 1rem;
}

.login-form .form-control {
    background: var(--bg-input);
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    padding: 1rem;
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.login-form .form-control::placeholder {
    color: var(--text-muted);
}

.login-form .form-control:focus {
    background: var(--bg-input);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

.login-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 0.75rem;
    color: var(--text-white);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.login-btn:hover {
    /* hover effects removed */
}

.login-links {
    text-align: center;
    margin-top: 1.5rem;
}

.login-links a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.login-links a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.slide-in {
    animation: slideIn 0.3s ease forwards;
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.shadow-custom {
    box-shadow: var(--shadow-xl);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) 1;
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
}

/* ===== DASHBOARD SPECIFIC ===== */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-card);
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    /* hover effects removed */
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
    box-shadow: 0 1px 4px rgba(22, 29, 111, 0.3);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
    color: var(--text-white);
    box-shadow: 0 2px 8px rgba(22, 29, 111, 0.3);
    border: 1px solid rgba(30, 42, 138, 0.4);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}