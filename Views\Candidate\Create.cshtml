@model RafoEvaluation.ViewModels.CandidateCreateViewModel
@{
    ViewData["Title"] = "إضافة مرشح جديد";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-plus ms-2"></i>إضافة مرشح جديد
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post" id="candidateForm">
                        <div class="row">
                            <!-- المعلومات الشخصية -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user ms-2"></i>المعلومات الشخصية
                                </h5>

                                <div class="mb-3">
                                    <label asp-for="FullName" class="form-label">الاسم الكامل</label>
                                    <input asp-for="FullName" class="form-control" placeholder="أدخل الاسم الكامل" />
                                    <span asp-validation-for="FullName" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="CategoryId" class="form-label">الفئة</label>
                                    <select asp-for="CategoryId" class="form-select" id="categorySelect" onchange="toggleFieldsByCategory()">
                                        <option value="">اختر الفئة</option>
                                        @foreach (var category in Model.Categories)
                                        {
                                            <option value="@category.CategoryId" data-code="@category.CategoryCode">@category.CategoryName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="CategoryId" class="text-danger"></span>
                                </div>

                                <!-- Category Information Display -->
                                <div id="categoryInfo" class="alert alert-info d-none">
                                    <div id="categoryDetails"></div>
                                </div>

                                <div class="mb-3">
                                    <label id="nationalIdLabel" class="form-label">National ID Number <span class="text-danger" id="nationalIdRequiredStar">*</span></label>
                                    <input asp-for="NationalIdNumber" id="nationalIdInput" class="form-control" placeholder="Enter national ID number" />
                                    <span asp-validation-for="NationalIdNumber" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label id="serviceNumberLabel" class="form-label">Service Number</label>
                                    <input asp-for="ServiceNumber" class="form-control" placeholder="Enter service number" />
                                    <span asp-validation-for="ServiceNumber" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="DateOfBirth" class="form-label"></label>
                                    <input asp-for="DateOfBirth" class="form-control" type="date" />
                                    <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="JobTitle" class="form-label"></label>
                                    <input asp-for="JobTitle" class="form-control" placeholder="Enter job title" />
                                    <span asp-validation-for="JobTitle" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- Official Information -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-id-badge me-2"></i>Official Information
                                </h5>

                                <div class="mb-3">
                                    <label asp-for="RankId" class="form-label"></label>
                                    <select asp-for="RankId" class="form-select">
                                        <option value="">Select Rank</option>
                                        @foreach (var rank in Model.Ranks)
                                        {
                                            <option value="@rank.RankId">@rank.RankName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="RankId" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="AirbaseId" class="form-label"></label>
                                    <select asp-for="AirbaseId" class="form-select">
                                        <option value="">Select Airbase</option>
                                        @foreach (var airbase in Model.Airbases)
                                        {
                                            <option value="@airbase.AirbaseId">@airbase.AirbaseName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="AirbaseId" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Department" class="form-label"></label>
                                    <input asp-for="Department" class="form-control" placeholder="Enter department" />
                                    <span asp-validation-for="Department" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsActive" class="form-check-label"></label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Military Information Section (Hidden by default) -->
                        <div id="militaryInfoSection" class="row mt-4 d-none">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-shield-alt me-2"></i>Military Information
                                </h5>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    This section is required for military personnel categories.
                                </div>
                            </div>
                        </div>

                        <!-- Education Information Section (Hidden by default) -->
                        <div id="educationInfoSection" class="row mt-4 d-none">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-graduation-cap me-2"></i>Education Information
                                </h5>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="University" class="form-label"></label>
                                    <input asp-for="University" class="form-control" placeholder="Enter university name" />
                                    <span asp-validation-for="University" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="Major" class="form-label"></label>
                                    <input asp-for="Major" class="form-control" placeholder="Enter major/specialization" />
                                    <span asp-validation-for="Major" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label asp-for="GraduationYear" class="form-label"></label>
                                    <input asp-for="GraduationYear" class="form-control" type="number" min="1950" max="2050" placeholder="Year" />
                                    <span asp-validation-for="GraduationYear" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label asp-for="MarksGrade" class="form-label"></label>
                                    <input asp-for="MarksGrade" class="form-control" placeholder="Grade" />
                                    <span asp-validation-for="MarksGrade" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a asp-action="Index" class="btn btn-secondary me-2">
                                        <i class="fas fa-times me-1"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>Save Candidate
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Category definitions with requirements
        const categoryRequirements = {
            'CAT-PLT': { 
                name: 'المرشحين الطيارين',
                military: false, 
                education: false, 
                serviceRequired: false,
                nationalIdRequired: true,
                description: 'البيانات الأساسية ومعلومات الاتصال فقط'
            },
            'CAT-MUG': { 
                name: 'المرشحين الجامعيين العسكريين',
                military: false, 
                education: true, 
                serviceRequired: false,
                nationalIdRequired: true,
                description: 'البيانات الأساسية + المعلومات التعليمية'
            },
            'CAT-CUG': { 
                name: 'المرشحين الجامعيين المدنيين',
                military: false, 
                education: true, 
                serviceRequired: false,
                nationalIdRequired: true,
                description: 'البيانات الأساسية + المعلومات التعليمية'
            },
            'CAT-LSO': { 
                name: 'ضباط الخدمة المحدودة',
                military: true, 
                education: false, 
                serviceRequired: true,
                nationalIdRequired: false,
                description: 'البيانات الأساسية + المعلومات العسكرية (رقم الخدمة مطلوب)'
            },
            'CAT-NCO': { 
                name: 'ضباط الصف (رقباء/عرفاء)',
                military: true, 
                education: false, 
                serviceRequired: true,
                nationalIdRequired: false,
                description: 'البيانات الأساسية + المعلومات العسكرية (رقم الخدمة مطلوب)'
            },
            'CAT-TCN': { 
                name: 'ضباط الصف الكلية التقنية العسكرية',
                military: true, 
                education: false, 
                serviceRequired: true,
                nationalIdRequired: false,
                description: 'البيانات الأساسية + المعلومات العسكرية (رقم الخدمة مطلوب)'
            },
            'CAT-CNP': { 
                name: 'ضباط الصف المدنيين للترفيع',
                military: true, 
                education: false, 
                serviceRequired: true,
                nationalIdRequired: false,
                description: 'البيانات الأساسية + المعلومات العسكرية (رقم الخدمة مطلوب)'
            }
        };

        function toggleFieldsByCategory() {
            const categorySelect = document.getElementById('categorySelect');
            const selectedOption = categorySelect.options[categorySelect.selectedIndex];
            const categoryCode = selectedOption.getAttribute('data-code');
            
            // Hide all optional sections first
            document.getElementById('militaryInfoSection').classList.add('d-none');
            document.getElementById('educationInfoSection').classList.add('d-none');
            document.getElementById('categoryInfo').classList.add('d-none');
            
            // Reset service number requirement
            const serviceNumberInput = document.querySelector('[name="ServiceNumber"]');
            const serviceNumberLabel = document.getElementById('serviceNumberLabel');
            serviceNumberInput.removeAttribute('required');
            serviceNumberLabel.innerHTML = 'Service Number';
            
            // National ID requirement
            const nationalIdInput = document.getElementById('nationalIdInput');
            const nationalIdLabel = document.getElementById('nationalIdLabel');
            const nationalIdStar = document.getElementById('nationalIdRequiredStar');
            if (categoryCode && categoryRequirements[categoryCode] && categoryRequirements[categoryCode].nationalIdRequired === false) {
                nationalIdInput.removeAttribute('required');
                nationalIdStar.style.display = 'none';
                nationalIdLabel.innerHTML = 'National ID Number <span class="text-muted">(Optional for selected category)</span>';
            } else {
                nationalIdInput.setAttribute('required', 'required');
                nationalIdStar.style.display = '';
                nationalIdLabel.innerHTML = 'National ID Number <span class="text-danger" id="nationalIdRequiredStar">*</span>';
            }
            
            if (!categoryCode || !categoryRequirements[categoryCode]) return;
            
            const requirements = categoryRequirements[categoryCode];
            
            // Show category info
            document.getElementById('categoryInfo').classList.remove('d-none');
            document.getElementById('categoryDetails').innerHTML = 
                '<strong>Category:</strong> ' + requirements.name + '<br>' +
                '<strong>Requirements:</strong> ' + requirements.description;
            
            // Show/hide sections based on category
            if (requirements.military) {
                document.getElementById('militaryInfoSection').classList.remove('d-none');
                
                if (requirements.serviceRequired) {
                    serviceNumberInput.setAttribute('required', 'required');
                    serviceNumberLabel.innerHTML = 'Service Number <span class="text-danger">*</span>';
                }
            }
            
            if (requirements.education) {
                document.getElementById('educationInfoSection').classList.remove('d-none');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleFieldsByCategory();
            
            // Form validation
            document.getElementById('candidateForm').addEventListener('submit', function(e) {
                const categoryId = document.getElementById('categorySelect').value;
                const selectedOption = document.getElementById('categorySelect').options[document.getElementById('categorySelect').selectedIndex];
                const categoryCode = selectedOption ? selectedOption.getAttribute('data-code') : null;
                
                console.log('=== Form Submission Debug ===');
                console.log('CategoryId value:', categoryId);
                console.log('CategoryCode from data-code:', categoryCode);
                console.log('Selected option text:', selectedOption ? selectedOption.text : 'none');
                
                if (!categoryId) {
                    alert('Please select a candidate category first');
                    e.preventDefault();
                    return false;
                }
                
                console.log('Submitting form with CategoryId:', categoryId);
                return true;
            });
        });
    </script>
}
