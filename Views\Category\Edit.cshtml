@model RafoEvaluation.ViewModels.CategoryEditViewModel
@{
    ViewData["Title"] = "Edit Category";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Category
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <input asp-for="CategoryId" type="hidden" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="CategoryName" class="form-label"></label>
                                    <input asp-for="CategoryName" class="form-control" placeholder="Enter category name" />
                                    <span asp-validation-for="CategoryName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="CategoryCode" class="form-label"></label>
                                    <input asp-for="CategoryCode" class="form-control" placeholder="Enter category code (e.g., CAT-ABC)" />
                                    <span asp-validation-for="CategoryCode" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Description" class="form-label"></label>
                            <textarea asp-for="Description" class="form-control" rows="4" 
                                      placeholder="Enter category description (optional)"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-4">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                <label asp-for="IsActive" class="form-check-label">
                                    Active
                                </label>
                            </div>
                            <small class="form-text text-muted">Check this box to make the category active and available for use.</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <div>
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to List
                                </a>
                                <a asp-action="Details" asp-route-id="@Model.CategoryId" class="btn btn-info">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </a>
                            </div>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>Update Category
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
